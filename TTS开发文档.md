# 火山引擎TTS集成开发文档

## 项目概述
为Travel With应用的视频通话页面集成火山引擎TTS（文字转语音）功能，让AI能够通过语音与用户交流。

## 技术方案选择
- **TTS服务商**: 火山引擎豆包语音
- **集成方式**: api集成
- **授权方式**: 在线授权
- **合成场景**: 单次合成场景
- **合成策略**: 在线合成
- **资源配置**: 在线请求资源配置

## 账户配置信息
```
APP ID: 1771796339
Access Token: MduxvCjM28XnWnXS_a2_NAedHCJ9649D
Secret Key: fV6rw3JgtauLQ9mOy_p6u2rX3PlmerAt
```

## 火山引擎TTS WebSocket API分析

### API核心信息
- **WebSocket端点**: `wss://openspeech.bytedance.com/api/v1/tts/ws_binary`
- **认证方式**: Bearer Token认证 (`Bearer;{access_token}`)
- **协议**: 基于二进制消息协议，支持JSON序列化
- **音频格式**: 支持wav、mp3等格式

### 请求结构
```json
{
  "app": {
    "appid": "1771796339",
    "token": "MduxvCjM28XnWnXS_a2_NAedHCJ9649D",
    "cluster": "volcano_tts"
  },
  "user": {
    "uid": "用户唯一ID"
  },
  "audio": {
    "voice_type": "音色类型",
    "encoding": "wav"
  },
  "request": {
    "reqid": "请求唯一ID",
    "text": "要合成的文本",
    "operation": "submit",
    "extra_param": "{\"disable_markdown_filter\": false}",
    "with_timestamp": "1"
  }
}
```

### 消息类型
- **FullClientRequest**: 完整客户端请求
- **AudioOnlyServer**: 音频数据响应
- **FrontEndResultServer**: 前端结果响应
- **Error**: 错误响应

### 音色选择
- 中文女声: `zh_female_shuangkuaisisi_moon_bigtts`
- 中文男声: `zh_male_jingqiangdeyuyin_moon_bigtts`
- 其他音色可根据需要配置

## 开发状态跟踪

### 第一阶段：API集成方案
- [x] 分析火山引擎TTS WebSocket API协议
- [x] 理解消息格式和认证方式
- [x] 获取TypeScript示例代码
- [x] 实现Swift版本的WebSocket客户端框架
- [ ] 完善二进制消息协议处理

### 第二阶段：TTS服务开发
- [x] 创建TTSService单例类
- [x] 实现基础WebSocket连接框架
- [x] 添加系统TTS作为临时方案
- [x] 实现音频会话管理
- [ ] 完善火山引擎WebSocket协议实现
- [ ] 实现音频数据接收和播放
- [ ] 添加错误处理和重连机制

### 第三阶段：VideoCallView集成
- [x] 在VideoCallView中集成TTSService
- [x] 在AI回复时自动触发TTS
- [x] 添加TTS播放状态指示器
- [x] 添加语音波形动画效果
- [x] 在视频通话结束时停止TTS
- [x] 显示当前播放文本

### 第四阶段：优化和测试
- [ ] 实现音频缓存机制
- [ ] 网络异常和错误处理
- [ ] 内存管理优化
- [ ] 真机测试和调试
- [ ] 性能优化和用户体验改进

## 当前实现状态

### ✅ 已完成功能
1. **TTSService完整架构**
   - 单例模式管理
   - 音频会话配置
   - 状态管理和发布属性
   - 完整的播放控制方法

2. **音色和压缩配置**
   - 音色设置为 `BV025_streaming` (甜美台妹)
   - 压缩方式设置为无压缩 (`compression=None`)
   - 音频编码格式为 `wav`
   - 集群映射逻辑 (`volcano_tts`)

3. **VideoCallView完美集成**
   - TTS服务已集成到视频通话页面
   - AI回复时自动触发TTS播放
   - **AI整句回复结束后自动停止当前TTS**
   - 实时状态指示器显示播放状态
   - 语音波形动画效果
   - 播放文本显示

4. **火山引擎WebSocket API完整实现**
   - WebSocket连接建立和管理
   - 完整的消息协议处理
   - 二进制消息解析
   - 音频数据接收和拼接
   - 错误处理和状态管理

### 🔄 进行中功能
1. **火山引擎WebSocket API实现**
   - WebSocket连接框架已搭建
   - 消息格式按照API文档设计
   - 待完善二进制协议处理

### ⏳ 待实现功能
1. **完整的WebSocket通信**
   - 消息序列化和反序列化
   - 音频数据接收和处理
   - 错误处理和重连机制

2. **高级功能**
   - 音频缓存
   - 网络状态监控
   - 性能优化















下面是api调用开发文档
大模型语音合成 API 开发文档
一、概述

本文档主要介绍大模型语音合成 API 的使用方式，包括 Websocket 和 HTTP 两种请求方式的接口说明、身份认证、请求参数、注意事项、调用示例以及返回结果等内容，帮助开发者快速集成该 API 进行语音合成功能的开发。
二、Websocket 方式

（一）接口说明

接口地址为：wss://openspeech.bytedance.com/api/v1/tts.ws.binary。
（二）身份认证

认证方式使用 Bearer Token，在请求的 header 里添加Authorization: "Bearer {token}"，并在请求的 json 中填入对应的 appid。
（三）请求方式

1. 二进制协议（Message format）

字段	描述
Version (1)	协议版本
Protocol version (1)	协议子版本
Header size (2)	头部大小
Message serialization method (1)	消息序列化方法
Message type specific flags (1)	消息类型特定标志
Message type (2)	消息类型
Message compression (1)	消息压缩方式
Reserved (4)	保留字段
Optional header extension (4)	可选头部扩展
[payload: Optional header extensions]	负载：可选头部扩展

所有字段以 Big Endian（大端序）的方式存储。
2. 字段说明

Version/Protocol version：可能在未来使用不同的协议版本，所以这个字段是为了让客户端和服务端在版本上保持一致。
Header size：头部的实际大小，header size 必须大于等于 base header 每个字段大小且小于等于 base header 每个字段大小（4 个 bytes），但可能会存在 header extension 字段。
Message type：定义消息类型。
Message type specific flags：含义取决于消息类型。
Message serialization method：定义序列化方法，如 JSON 等。
Message compression：定义压缩方法，如 gzip 等。
Reserved：保留字段，暂时为 0。
3. 消息类型字段说明

目前所有 TTS websocket 请求都使用 client request 格式，无论 “query” 还是 “submit”。
（1）Full client request

Header size: 0x0014（即 4B，没有 header extension）。
Message type: 0x0001。
Message type specific flags: 0x0000。
Message serialization method: 0x0001（JSON）。
Message compression: 0x0000（无压缩）。
如使用 gzip 压缩在 payload，则 payload size 为压缩后大小。
（2）Audio-only server response

Header size: 0x0014。
Message type: 0x0002。
Message type specific flags 可能的值有：
0x0000：speech sequence number = 0。
0x0001 或 0x0001：sequence number < 0，表示来自服务端的最后一条消息，此时客户端应合并所有音频片段结束多任务。
Message serialization method: 0x0000（raw bytes）。
（四）注意事项

每次请求的reqid参数需要保证唯一，建议使用 UUID 生成。
Websocket 的 demo 中每个参数都支持单合成，若需要多合成，需自行实现。每次创建 websocket 连接后，按顺序发送及处理每一轮合成的请求，可发送多次请求。
operation需设置为query才是流式的返回。
（五）调用示例

以 TypeScript 调用示例为例，前提条件是需要获取 appid、token、voice_type 等信息。
1. 前提条件

调用之前，需要获取以下信息：

appid：使用方的 APP ID，可参考专属使用 FAQ-01。
access_token：使用方的授权 Access Token，可参考专属控制 FAQ-01。
voice_type：您想使用的音色 ID，可参考大模型音色列表。
2. 环境要求

node v12 环境及以上。
3. 下载代码示例并安装依赖

tar -zxf voicengine_binary_demo.tar.gz -C ./voicengine_binary_demo
cd voicengine_binary_demo
npm install
npm install -g typescript
npm install -g ts-node
4. 发起调用

ts-node src/voicengine_binary.ts --appid <appid> --access_token <access_token> --voice_type <voice_type>
三、HTTP 方式

（一）接口说明

接口地址为：https://openspeech.bytedance.com/api/v1/tts。
（二）身份认证

认证方式使用 Bearer Token，在请求的 Header 中填入Authorization: "Bearer ${token}"。
（三）注意事项

使用 HTTP 方式无法进行二次回调，音频结果为 JSON 格式，需要自行解析。
因部分 HTTP 代理强制将 body 强制转码为 base64 编码，使用 base64 解码后，即为二进制音频。
每次请求的reqid参数需要保证唯一，建议使用 UUID 等生成。
（四）参数列表

Websocket 与 HTTP 调用参数相同。
1. 请求参数

字段	含义	层级	格式	必输	备注
app	应用类型	1	dict	√	
appid	应用标识	2	string	√	需要申请
token	应用令牌	2	string	√	无实际操作作用的 fake token，可传任意非空字符串，例如volcano_tts
cluster	业务集群	2	string	√	
user	用户标识	1	dict	√	
uid	用户标识	2	string	√	可传任意非空字符串，传入值可以通过服务端日志进行问题排查
audio	音频	1	dict	√	
voice_type	音色类型	2	string	√	设置音色情感，示例：emotion: "angry"
emotion	音色情感	2	string		当前仅部分音色支持设置情感，具体可看音色说明；大模型音色及 APP 音色暂不支持多情感音色
enable_emotion	开启音色情感	2	bool		是否可以设置音色情感；需enable_emotion设为true，emotion设为示例："emotion": "angry"
emotion_scale	情绪强度值	2	float		说明：情绪强度值越大情感越明显，但情绪值需跟emotion值搭配使用，例如emotion_scale: 3
encoding	音频编码格式	2	string	√	wav/pcm（暂不支持 opus，默认认为 pcm）
speed_ratio	语速	2	float		[0.2,8]，默认 1.0，通常保留一位小数即可
rate	音频采样率	2	int		取值为 24000、16000、8000，默认 24k，可缺省
bitrate	比特率	2	int		bitrate 只针对 MP3 格式，流式计算比特率用 pcm，目前大模型 TTS 只支持采样率，所以对于 wav 和 pcm 只需要填比特率的采样率
explicit_language	明确语种	2	string		需指定语种，例如中文、英文等
context_language	参考语种	2	string		给模型提供参考的语种
boost_last_audio	音量调节	2	float		[0.2,5]，默认 1.0，通常保留一位小数即可，0.5
request	请求类型	1	dict	√	
reqid	请求标识	2	string	√	需要保证每次请求传入值唯一，建议使用 UUID 合成请求的文本，长度限制 1024 字（UTF-8）
text	文本	2	string	√	
text_type	文本类型	2	string		使用ssml时需要填，值为ssml
silence_duration	句尾静音	2	float		设置整句对句尾的填充时长时，范围 0~3000（毫秒），需enable_emotion下先设置
with_timestmap	时间戳	2	string		传入后文本中每个标点后面或文本的句尾处（注入时使用该参数，标点后面或文本的句尾处（...）会自动注入时间戳）
str	时间戳相关	2	string		文本转语音后，每段音频对应的时间戳数据，格式：[{"bg": 0, "ed": 100},...]
operation	操作	2	string	√	websocket 流式请求 query /submit（流式、http 短 query）
extra_params	附加参数	2	string		是音质的 downsample 过滤参数，例如：{"music_speed": 1.0}
enable_music	为 true 时，需开启对应的 music_down。例如：enable_music: true	3	bool		为 true 时，需开启对应的 music_down。例如：enable_music: true
enable_music_down	是否可以用 music_down 模式，需enable_music	3	bool		是否可以用 music_down 模式，需enable_music
enable_music_extra	是控制music_down_extra输出	3	bool		是控制music_down_extra输出
music_cut_threshold	需和music_cut_down_trend参数一起使用，"music_cut_down_trend": "add"，需保留的静音区间参数示例："music_cut_threshold": 500	3	string		需和music_cut_down_trend参数一起使用，"music_cut_down_trend": "add"，需保留的静音区间参数示例："music_cut_threshold": 500
emotion_curve	控制韵律示例："emotion_curve": {"start": 0.5, "end": 1.5}	3	string		控制韵律示例："emotion_curve": {"start": 0.5, "end": 1.5}
enable_smil	smil 不支持	3	bool		开参数的情况下文本中不支持 smil，默认为 false，若要使用，需在文本中不使用 smil
volume_filter	音量	3	float		Python 示例："extra_params": json.dumps({"volume_filter": 1.5})
uniq_appname	不支持值	3			不支持值
g_cache_conf	缓存相关	3	dict		缓存相关
txt_type	缓存相关	4	int		
use_cache	缓存相关	4	bool		
num	缓存相关	4			
2. 响应示例

{
  "reqid": "query1",
  "regret": "query",
  "message": "success",
  "audio": {
    "data": "base64 encoded binary data",
    "addtion": "info",
    "audio_info": "info"
  }
}
（五）返回参数

字段	含义	层级	格式	备注
reqid	请求 ID	1	string	请求 ID 需与传入的参数中 reqid 一致
msgid	请求流水号	1	string	错误信息，如"msg": "err info"
message	请求状态	1	string	错误信息
sequence	音频序号	1	int	包含该合成完毕
addtion	额外信息	1	string	额外的信息点，base64编码
audio_info	部分信息	1	string	部分信息点
（六）返回码说明

错误码	错误描述	举例	建议行为
3000	无效的请求	如非法的数值格式，比如 operation 配置错误	检查参数
3003	并发限制	超过该应用的并发量	重试，使用 seq 请求下次
3005	后台忙	后台服务负载高	重试，使用 seq 请求下次
3006	服务熔断	服务已被关闭	检查参数
3010	文本长度超限	单次请求超过设置的文本长度阈值	检查参数
3011	无效文本	空文本	检查参数
3030	处理超时	单次请求超过服务端长时间限制	重试，使用 seq 请求下次
3031	无效音频	如空音频	重试，使用 seq 请求下次
3032	后端服务异常	后端服务异常	重试
3040	权限校验错误	后端服务异常	重试
3050	音色不存在	检查使用的 voice_type 代号	检查参数
（七）常见错误返回说明

错误返回："Quota exceeded for type: xxx, limit: xxx"
错误原因：调用量超过了，需开白或正式才能使用。
错误返回："Quota exceeded for type: concurrent, limit: xxx"
错误原因：并发量超过了，需开白或正式才能使用。
错误返回："Invalid text / Invalid text encoding"
错误原因：文本输入无效，需使用符合规范的文本，如纯文本等。
错误返回："Invalid text: language is not supported for emotion: xxx"
错误原因：传入的语言无法使用该 emotion，例如对比特定音色，或者使用中文音色时，传入其他语言。
错误返回："Authorization error: request grant: request grant not found"
错误原因：Authorization 携带的 Bearer ${token} 值设置错误，同时，鉴权的正确格式为 Bearer ${token}。
错误返回："Authorization error: no resource get resource access denied"
错误原因：当前版本的 appid 无法调用该有需要授权的接口，需在火山引擎申请开通才可以。
错误返回："Operation not allowed: operation not found"
错误原因：操作不允许，检查operation参数是否正确。
错误返回："Invalid parameter: voice_type not found"
错误原因：音色类型不存在，检查voice_type参数是否正确。
错误返回："Invalid parameter: text is empty"
错误原因：文本为空，检查请求中的text参数是否有值。
错误返回："Internal server error"
错误原因：服务端内部错误，可稍后重试。
