# 灿灿2.0语音情感系统使用指南

## 📋 概述

本文档详细介绍Travel With应用中集成的灿灿2.0语音情感系统的配置、使用方法和最佳实践。

**默认音色**: zh_female_wanwanxiaohe_moon_bigtts (湾湾小何 - 趣味方言)  
**情感模型**: zh_female_cancan_mars_bigtts (灿灿2.0)  
**集成状态**: 配置完成，暂未实现情感功能  
**支持情感**: 22种情感/风格音色  

## 🎭 支持的情感/风格音色

### 1. 基础情感类
| 情感音色 | 标识符 | 适用场景 | 推荐使用时机 |
|:--------:|:------:|:--------:|:------------:|
| 通用/愉悦 | `pleased` | 日常对话、愉快交流 | 默认情感音色，日常聊天交流 |
| 开心 | `happy` | 喜悦分享、兴奋表达 | 发现美景、分享快乐、庆祝时刻 |
| 悲伤 | `sad` | 难过安慰、失落表达 | 安慰用户、表达遗憾、同情理解 |
| 愤怒 | `angry` | 愤怒表达、强烈不满 | 表达不满、强调重要性、警告提醒 |
| 害怕 | `scare` | 恐惧表达、紧张情绪 | 危险提醒、紧急情况、安全警告 |
| 厌恶 | `hate` | 厌恶表达、反感情绪 | 负面评价、不推荐场所、强烈反对 |
| 惊讶 | `surprise` | 惊讶表达、意外情绪 | 意外发现、惊喜分享、突发情况 |
| 哭腔 | `tear` | 哭泣表达、深度悲伤 | 深度安慰、情感共鸣、感动时刻 |

### 2. 社交情感类
| 情感音色 | 标识符 | 适用场景 | 推荐使用时机 |
|:--------:|:------:|:--------:|:------------:|
| 抱歉 | `sorry` | 道歉场景、表达遗憾 | 道歉时刻、表达歉意、承认错误 |
| 嗔怪 | `annoyed` | 轻微不满、嗔怪表达 | 轻微抱怨、可爱嗔怪、撒娇不满 |
| 安慰鼓励 | `comfort` | 安慰鼓励、温暖支持 | 鼓励用户、温暖陪伴、心理支持 |
| 撒娇 | `lovey-dovey` | 撒娇卖萌、可爱表达 | 可爱互动、撒娇时刻、亲密交流 |
| 傲娇 | `tsundere` | 傲娇风格、反差萌 | 傲娇互动、反差可爱、害羞表达 |
| 娇媚 | `charming` | 娇媚魅惑、温柔诱人 | 温柔时刻、魅力表达、优雅交流 |

### 3. 专业风格类
| 情感音色 | 标识符 | 适用场景 | 推荐使用时机 |
|:--------:|:------:|:--------:|:------------:|
| 客服 | `customer_service` | 客服场景、专业服务 | 专业咨询、服务介绍、问题解答 |
| 专业 | `professional` | 正式场合、商务对话 | 正式介绍、商务交流、专业建议 |
| 严肃 | `serious` | 严肃话题、重要事项 | 重要提醒、严肃讨论、正式通知 |

### 4. 特色风格类
| 情感音色 | 标识符 | 适用场景 | 推荐使用时机 |
|:--------:|:------:|:--------:|:------------:|
| 绿茶 | `conniving` | 绿茶风格、心机表达 | 特殊场景、风格化表达、角色扮演 |
| 情感电台 | `radio` | 电台主播、磁性声音 | 深度分享、电台风格、夜晚陪伴 |
| 瑜伽 | `yoga` | 瑜伽冥想、平静放松 | 放松指导、冥想时刻、舒缓体验 |
| 讲故事 | `storytelling` | 故事讲述、生动演绎 | 历史讲述、故事分享、文化介绍 |

## 🔧 技术实现

### 核心枚举定义
```swift
enum CanCanEmotion: String, CaseIterable {
    case pleased = "pleased"              // 通用/愉悦 (默认)
    case sorry = "sorry"                 // 抱歉
    case annoyed = "annoyed"             // 嗔怪
    case customerService = "customer_service" // 客服
    case professional = "professional"   // 专业
    case serious = "serious"             // 严肃
    case happy = "happy"                 // 开心
    case sad = "sad"                     // 悲伤
    case angry = "angry"                 // 愤怒
    case scare = "scare"                 // 害怕
    case hate = "hate"                   // 厌恶
    case surprise = "surprise"           // 惊讶
    case tear = "tear"                   // 哭腔
    case conniving = "conniving"         // 绿茶
    case comfort = "comfort"             // 安慰鼓励
    case radio = "radio"                 // 情感电台
    case loveyDovey = "lovey-dovey"     // 撒娇
    case tsundere = "tsundere"           // 傲娇
    case charming = "charming"           // 娇媚
    case yoga = "yoga"                   // 瑜伽
    case storytelling = "storytelling"   // 讲故事
}
```

### 主要接口方法

#### 1. 设置情感音色
```swift
// 手动设置当前情感
ttsService.setEmotion(.happy)

// 合成时指定情感
ttsService.synthesizeAndPlay(text: "今天天气真好！", emotion: .happy)
```

#### 2. 智能情感推荐
```swift
// 基于文本内容推荐情感
let recommendedEmotion = ttsService.recommendEmotion(for: "今天真是太开心了！")
// 返回: .happy
```

#### 3. 状态查询
```swift
// 获取当前情感
let currentEmotion = ttsService.getCurrentEmotion()

// 获取所有支持的情感
let allEmotions = ttsService.getSupportedEmotions()
```

## 🎯 使用最佳实践

### 旅行场景应用建议

#### 景点介绍场景
- **推荐情感**: `storytelling` (讲故事)
- **使用时机**: 介绍景点历史、文化背景、建筑特色
- **示例**: "这座古城建于明朝，有着600多年的历史..."

#### 美食分享场景
- **推荐情感**: `happy` (开心)
- **使用时机**: 推荐美食、分享用餐体验
- **示例**: "这家餐厅的小笼包真的太好吃了！"

#### 专业咨询场景
- **推荐情感**: `professional` (专业) 或 `customer_service` (客服)
- **使用时机**: 提供专业建议、回答用户问题
- **示例**: "根据您的需求，我建议您选择这条路线..."

#### 安慰鼓励场景
- **推荐情感**: `comfort` (安慰鼓励)
- **使用时机**: 用户遇到困难、需要鼓励时
- **示例**: "没关系，我们一起想办法解决这个问题。"

#### 撒娇互动场景
- **推荐情感**: `lovey-dovey` (撒娇) 或 `tsundere` (傲娇)
- **使用时机**: 轻松愉快的互动、增加亲密感
- **示例**: "人家也想去那里玩嘛～"

### 情感切换策略

#### 自然过渡
- 避免情感音色的突然切换
- 在对话的自然停顿点切换情感
- 保持情感与内容的一致性

#### 上下文感知
- 根据对话历史选择合适的情感
- 考虑用户当前的情绪状态
- 结合旅行场景的特殊需求

## 🚀 未来扩展计划

### 智能情感分析
- **AI情感识别**: 基于深度学习的文本情感分析
- **上下文理解**: 结合对话历史的情感状态推理
- **用户偏好学习**: 记住用户的情感偏好设置

### 高级情感控制
- **情感强度调节**: 支持情感强度的细粒度控制
- **混合情感表达**: 支持多种情感的混合表达
- **动态情感变化**: 在单次语音中实现情感的渐变

### 场景化预设
- **旅行场景模板**: 预设不同旅行场景的情感组合
- **个性化配置**: 用户自定义情感偏好和场景设置
- **智能推荐系统**: 基于用户行为的情感推荐优化

## 📝 开发注意事项

### 代码规范
- 所有情感相关代码都有详细的中文注释
- 使用统一的命名规范和代码结构
- 保持接口的向后兼容性

### 性能考虑
- 情感切换不应影响TTS合成速度
- 缓存常用情感配置，减少计算开销
- 优化情感推荐算法的执行效率

### 用户体验
- 提供清晰的情感状态反馈
- 支持用户手动覆盖智能推荐
- 确保情感表达的自然性和一致性

## 🔍 调试和测试

### 日志输出
系统会在控制台输出详细的情感相关日志：
```
🎭 切换情感音色: 开心 (happy)
📝 适用场景: 喜悦分享、兴奋表达
🎤 开始TTS合成: 今天天气真好！
🎭 当前情感: 开心 (happy)
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
```

### 测试建议
- 测试所有22种情感音色的切换
- 验证智能推荐功能的准确性
- 检查情感状态的持久性和一致性
- 测试异常情况下的情感回退机制

---

**文档版本**: v2.0  
**最后更新**: 2025/8/2  
**维护者**: Travel With开发团队
