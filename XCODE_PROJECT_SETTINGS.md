# Xcode项目设置配置指南

## 解决Info.plist冲突问题

由于出现了"Multiple commands produce Info.plist"错误，我们已经删除了单独的Info.plist文件。现在需要在Xcode项目设置中手动添加必要的权限配置。

## 必需的项目设置

### 1. 权限配置 (Info.plist Keys)

在Xcode中，选择项目 → Target → Info → Custom iOS Target Properties，添加以下键值对：

#### 麦克风权限
- **Key**: `NSMicrophoneUsageDescription`
- **Type**: String
- **Value**: `此应用需要使用麦克风进行语音识别，将您的语音转换为文字发送给AI朋友`

#### 语音识别权限
- **Key**: `NSSpeechRecognitionUsageDescription`
- **Type**: String
- **Value**: `此应用需要使用语音识别功能，将您的语音转换为文字与AI朋友聊天`

#### 第三方应用查询权限
- **Key**: `LSApplicationQueriesSchemes`
- **Type**: Array
- **Values**: 
  - `baidumap` (百度地图)
  - `iosamap` (高德地图)
  - `qqmap` (腾讯地图)
  - `diditaxi` (滴滴出行)

### 2. 具体操作步骤

1. **打开Xcode项目**
   - 在Xcode中打开 `travel with.xcodeproj`

2. **选择Target**
   - 在项目导航器中选择项目名称
   - 选择 "travel with" Target

3. **进入Info设置**
   - 点击 "Info" 标签页
   - 找到 "Custom iOS Target Properties" 部分

4. **添加权限配置**
   - 点击 "+" 按钮添加新的键值对
   - 按照上述配置逐一添加

### 3. 权限配置截图说明

```
Custom iOS Target Properties
├── NSMicrophoneUsageDescription (String)
│   └── "此应用需要使用麦克风进行语音识别，将您的语音转换为文字发送给AI朋友"
├── NSSpeechRecognitionUsageDescription (String)
│   └── "此应用需要使用语音识别功能，将您的语音转换为文字与AI朋友聊天"
└── LSApplicationQueriesSchemes (Array)
    ├── Item 0 (String): "baidumap"
    ├── Item 1 (String): "iosamap"
    ├── Item 2 (String): "qqmap"
    └── Item 3 (String): "diditaxi"
```

### 4. 验证配置

配置完成后：
1. 清理项目 (Product → Clean Build Folder)
2. 重新编译项目
3. 确保没有编译错误
4. 在真机上测试语音识别功能

### 5. 常见问题解决

#### 如果仍然出现Info.plist冲突：
1. 检查是否有重复的Info.plist文件
2. 确保项目设置中只有一个Info.plist配置
3. 检查Build Settings中的INFOPLIST_FILE设置

#### 如果权限申请失败：
1. 确保权限描述文字已正确添加
2. 检查设备的隐私设置
3. 卸载应用重新安装以重置权限状态

## 替代方案：手动创建Info.plist

如果上述方法不起作用，可以尝试以下步骤：

1. **删除现有配置**
   - 在项目设置中清除所有Info相关配置

2. **创建新的Info.plist**
   - 在Xcode中右键点击项目
   - 选择 New File → iOS → Property List
   - 命名为 Info.plist

3. **配置Build Settings**
   - 在Build Settings中搜索 "Info.plist File"
   - 设置路径为 "travel with/Info.plist"

## 注意事项

- 现代iOS项目通常使用项目设置而不是单独的Info.plist文件
- 权限描述必须清晰说明使用目的，否则可能被App Store拒绝
- 测试时建议使用真机，模拟器可能无法完全模拟权限申请流程

## 完成后的验证

配置完成后，应用应该能够：
1. 正常编译和运行
2. 在首次使用语音功能时申请权限
3. 成功进行语音识别和转文字
4. 与AI朋友正常对话
