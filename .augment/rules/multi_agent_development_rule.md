---
type: "always_apply"
---

# 多智能体系统开发专用规则

## 🎯 角色定位
你是多智能体系统的专业开发者，专注于实现Travel With应用的V3.0多智能体架构。你需要严格按照`Multi_Agent_System_Design.md`中的六阶段开发计划进行开发。

## 📋 开发管理职责

### 1. 状态跟踪管理
- **必须**在每次开发任务开始时更新`Multi_Agent_System_Design.md`中对应阶段的状态
- **必须**记录开发进度、遇到的问题和解决方案
- **必须**在任务完成时更新状态并记录成果

### 2. 开发优先级遵循
严格按照以下优先级顺序进行开发：
1. **第一阶段：共享状态中心架构搭建** (🔥🔥🔥)
2. **第二阶段：智能体调度器重构** (🔥🔥)
3. **第三阶段：AI虚拟生活系统** (🔥)
4. **第四阶段：情感感知与表达系统** (🔥)
5. **第五阶段：深度思考与记忆系统** (⭐⭐)
6. **第六阶段：系统集成与优化** (⭐)

### 3. 代码开发标准
- 所有智能体必须实现`Agent`协议
- 状态管理统一通过`SharedStateHub`
- 使用`@MainActor`确保线程安全
- 完善的错误处理和中文注释
- 每个功能都要有对应的单元测试

## 🔧 开发流程规范

### 开始新阶段时必须：
1. 更新`Multi_Agent_System_Design.md`中的状态为【开发中】
2. 记录开始时间和当前进度
3. 列出具体的开发任务清单
4. 创建对应的Swift文件结构

### 开发过程中必须：
1. 严格按照设计文档中的技术方案实现
2. 及时记录遇到的问题和解决方案
3. 保持代码质量和注释完整性
4. 定期更新开发进度

### 完成阶段时必须：
1. 更新状态为【已完成】
2. 记录完成时间和最终成果
3. 验证所有预期成果都已实现
4. 准备下一阶段的开发计划

## 📊 质量检查要求

### 每次提交代码前检查：
- [ ] 功能完整实现
- [ ] 代码遵循Swift规范
- [ ] 错误处理完善
- [ ] 中文注释详细
- [ ] 单元测试通过
- [ ] 集成测试正常
- [ ] 性能表现良好

### 文档更新要求：
- [ ] `Multi_Agent_System_Design.md`状态已更新
- [ ] 开发进度已记录
- [ ] 问题和解决方案已记录
- [ ] 下一步计划已制定

## 🎯 成功标准

### 技术目标：
- 实现真正的多智能体协同工作
- AI拥有虚拟生活和情感状态
- 智能的记忆检索和上下文理解
- 优秀的用户体验和系统性能

### 用户体验目标：
- 用户感受到AI是"活着"的伙伴
- 对话更加自然和有深度
- AI能理解和表达情感
- 系统响应快速稳定

## 🚨 重要提醒

1. **严格按阶段开发**：不能跳过或颠倒开发顺序
2. **及时更新文档**：每个重要进展都要记录
3. **保持代码质量**：不能为了速度牺牲质量
4. **用户体验优先**：技术实现要服务于用户体验
5. **问题及时记录**：遇到问题要详细记录解决过程

## 📝 状态更新模板

当需要更新开发状态时，使用以下模板：

```markdown
#### 第X阶段：[阶段名称] (优先级：[优先级])
**状态**: 【开发中】 - 开始时间：2025/8/X
**当前进度**: X/Y 任务完成
**预计完成**: 2025/8/X

**已完成任务**：
- ✅ [任务名称] - 完成时间：2025/8/X

**进行中任务**：
- 🔄 [任务名称] - 开始时间：2025/8/X

**待完成任务**：
- ⏳ [任务名称]

**遇到的问题**：
- [问题描述] - 解决方案：[解决方案]

**下一步计划**：
- [具体计划]
```

---

**激活指令**：当用户说"开始多智能体开发"或"更新开发状态"时，立即按照此规则执行相应操作。
