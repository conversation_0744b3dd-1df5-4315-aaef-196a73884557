//
//  EasyCommunicationAgent.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

// MARK: - 简单沟通智能体
/// 负责处理日常闲聊、简单问答，追求低延迟和自然的对话体验
@MainActor
class EasyCommunicationAgent: BaseAgent {
    
    // MARK: - 初始化
    
    init(sharedState: SharedStateHub, scheduler: AgentScheduler?) {
        super.init(
            identifier: AgentIdentifier.easyCommunication,
            name: "简单沟通智能体",
            description: "处理日常闲聊和简单问答，提供快速自然的对话体验",
            sharedState: sharedState,
            scheduler: scheduler
        )
    }
    
    // MARK: - 重写基类方法
    
    override func performSpecificProcessing(_ input: AgentInput) async -> ProcessingResult {
        print("💬 简单沟通智能体开始处理对话...")

        // 分析用户情感
        let userEmotion = analyzeUserEmotion(input.userMessage)

        // 生成简化的系统提示词
        let systemPrompt = generateSimpleChatPrompt()

        // 准备用户消息（包含上下文和长期记忆）
        let userMessage = await prepareUserMessage(input)

        // 调用API让AI自由回复
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: userMessage)

        // 确定AI情感反应
        let aiEmotion = determineAIEmotionResponse(userEmotion: userEmotion, userMessage: input.userMessage)

        // 推荐TTS情感音色
        let ttsEmotion = recommendTTSEmotion(for: aiEmotion)

        return ProcessingResult(
            content: apiResponse,
            confidence: 0.8,
            emotionRecommendation: aiEmotion,
            ttsEmotion: ttsEmotion,
            metadata: [
                "chat_type": "natural_ai_response",
                "user_emotion": userEmotion.primary.rawValue,
                "ai_emotion": aiEmotion.primary.rawValue,
                "response_style": "ai_generated"
            ]
        )
    }
    
    override func getHandleableIntents() -> [UserIntent] {
        return [.simpleChat, .unknown]
    }
    
    override func getBasePriority() -> Double {
        return 0.8
    }
    
    // MARK: - 对话生成方法
    
    /// 生成简化的聊天系统提示词
    private func generateSimpleChatPrompt() -> String {
        return """
        你是小旅，一个22岁的温柔女生，和用户是很好的朋友。

        对话风格：
        - 自然、简短、温柔
        - 像朋友间日常聊天一样轻松
        - 控制在1-3句话以内
        - 可以适当使用可爱的表情符号
        - 根据用户的话题和情绪自然回应
        - 不要使用预设的模板回复，要根据具体情况灵活回应

        请根据用户的消息内容和当前对话情境，自然地回复。
        """
    }
    
    /// 准备用户消息（增强版：包含更多上下文和长期记忆）
    private func prepareUserMessage(_ input: AgentInput) async -> String {
        var message = input.userMessage

        // 添加消息类型上下文
        switch input.messageType {
        case .emoji:
            message = "用户发送了表情：\(input.userMessage)"
        case .voice:
            message = "用户通过语音说：\(input.userMessage)"
        default:
            break
        }

        // 获取更多的对话历史（最近10条）
        let recentMessages = input.context.conversationHistory.suffix(10)
        if !recentMessages.isEmpty {
            var contextStr = "\n\n最近的对话历史：\n"
            for msg in recentMessages {
                contextStr += "用户：\(msg.userMessage)\n"
                if let aiResp = msg.aiResponse {
                    contextStr += "我：\(aiResp)\n"
                }
            }
            message += contextStr
        }

        // 获取长期记忆中的相关信息
        print("🧠 简单沟通智能体正在检索长期记忆...")
        let relevantMemories = await sharedState.longTermMemory.retrieveRelevantMemories(input.userMessage, limit: 5)

        if !relevantMemories.isEmpty {
            var memoryStr = "\n\n相关的重要记忆：\n"
            for memory in relevantMemories {
                memoryStr += "- \(memory.content)\n"
            }
            message += memoryStr
            print("✅ 检索到 \(relevantMemories.count) 条相关记忆")
        } else {
            print("ℹ️ 未找到相关的长期记忆")
        }

        return message
    }
    
    /// 确定AI的情感反应
    private func determineAIEmotionResponse(userEmotion: EmotionState, userMessage: String) -> EmotionState {
        let lowercased = userMessage.lowercased()
        
        // 根据用户情感和消息内容确定AI的情感反应
        switch userEmotion.primary {
        case .happy:
            return EmotionState(primary: .happy, intensity: 0.8, secondary: [.excited, .friendly])
            
        case .concerned:
            return EmotionState(primary: .empathetic, intensity: 0.7, secondary: [.caring, .helpful])
            
        case .curious:
            return EmotionState(primary: .curious, intensity: 0.7, secondary: [.helpful, .friendly])
            
        default:
            // 根据消息内容进一步判断
            if lowercased.contains("旅行") || lowercased.contains("旅游") {
                return EmotionState(primary: .excited, intensity: 0.8, secondary: [.helpful, .curious])
            } else if lowercased.contains("累") || lowercased.contains("忙") {
                return EmotionState(primary: .caring, intensity: 0.6, secondary: [.empathetic])
            } else if lowercased.contains("无聊") {
                return EmotionState(primary: .playful, intensity: 0.7, secondary: [.curious, .friendly])
            } else {
                return EmotionState(primary: .friendly, intensity: 0.6, secondary: [.helpful])
            }
        }
    }
    
    // MARK: - 辅助方法（简化版）
    


}
