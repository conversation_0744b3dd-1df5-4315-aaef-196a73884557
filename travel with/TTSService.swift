//
//  TTSService.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/2.
//  基于火山引擎TTS WebSocket API实现
//  集成灿灿2.0语音情感系统 (zh_female_cancan_mars_bigtts)
//  默认音色: zh_female_wanwanxiaohe_moon_bigtts (湾湾小何 - 趣味方言)
//  压缩: 无压缩
//
//  灿灿2.0情感音色配置 (22种情感/风格):
//  - 通用 (pleased): 日常对话的愉悦语调
//  - 愉悦 (pleased): 表达愉快和满足情绪
//  - 抱歉 (sorry): 表达歉意和遗憾情绪
//  - 嗔怪 (annoyed): 表达轻微不满和嗔怪情绪
//  - 开心 (happy): 表达喜悦和兴奋情绪
//  - 愤怒 (angry): 表达愤怒和不满情绪
//  - 惊讶 (surprise): 表达惊讶和意外情绪
//  - 厌恶 (hate): 表达厌恶和反感情绪
//  - 悲伤 (sad): 表达难过和失落情绪
//  - 害怕 (scare): 表达恐惧和紧张情绪
//  - 哭腔 (tear): 表达哭泣和悲伤情绪
//  - 客服 (customer_service): 专业客服语调
//  - 专业 (professional): 正式专业语调
//  - 严肃 (serious): 严肃认真语调
//  - 傲娇 (tsundere): 傲娇可爱语调
//  - 安慰鼓励 (comfort): 温暖安慰语调
//  - 绿茶 (conniving): 绿茶风格语调
//  - 娇媚 (charming): 娇媚魅惑语调
//  - 情感电台 (radio): 电台主播语调
//  - 撒娇 (lovey-dovey): 撒娇可爱语调
//  - 瑜伽 (yoga): 瑜伽冥想语调
//  - 讲故事 (storytelling): 故事讲述语调
//

import Foundation
import AVFoundation
import Network
import Combine

// MARK: - 灿灿2.0情感音色枚举
enum CanCanEmotion: String, CaseIterable {
    case pleased = "pleased"                      // 通用/愉悦 (默认)
    case sorry = "sorry"                         // 抱歉
    case annoyed = "annoyed"                     // 嗔怪
    case customerService = "customer_service"     // 客服
    case professional = "professional"           // 专业
    case serious = "serious"                     // 严肃
    case happy = "happy"                         // 开心
    case sad = "sad"                            // 悲伤
    case angry = "angry"                        // 愤怒
    case scare = "scare"                        // 害怕
    case hate = "hate"                          // 厌恶
    case surprise = "surprise"                  // 惊讶
    case tear = "tear"                          // 哭腔
    case conniving = "conniving"                // 绿茶
    case comfort = "comfort"                    // 安慰鼓励
    case radio = "radio"                        // 情感电台
    case loveyDovey = "lovey-dovey"            // 撒娇
    case tsundere = "tsundere"                  // 傲娇
    case charming = "charming"                  // 娇媚
    case yoga = "yoga"                          // 瑜伽
    case storytelling = "storytelling"          // 讲故事

    /// 情感描述
    var description: String {
        switch self {
        case .pleased: return "通用/愉悦"
        case .sorry: return "抱歉"
        case .annoyed: return "嗔怪"
        case .customerService: return "客服"
        case .professional: return "专业"
        case .serious: return "严肃"
        case .happy: return "开心"
        case .sad: return "悲伤"
        case .angry: return "愤怒"
        case .scare: return "害怕"
        case .hate: return "厌恶"
        case .surprise: return "惊讶"
        case .tear: return "哭腔"
        case .conniving: return "绿茶"
        case .comfort: return "安慰鼓励"
        case .radio: return "情感电台"
        case .loveyDovey: return "撒娇"
        case .tsundere: return "傲娇"
        case .charming: return "娇媚"
        case .yoga: return "瑜伽"
        case .storytelling: return "讲故事"
        }
    }

    /// 适用场景
    var useCase: String {
        switch self {
        case .pleased: return "日常对话、愉快交流"
        case .sorry: return "道歉场景、表达遗憾"
        case .annoyed: return "轻微不满、嗔怪表达"
        case .customerService: return "客服场景、专业服务"
        case .professional: return "正式场合、商务对话"
        case .serious: return "严肃话题、重要事项"
        case .happy: return "喜悦分享、兴奋表达"
        case .sad: return "难过安慰、失落表达"
        case .angry: return "愤怒表达、强烈不满"
        case .scare: return "恐惧表达、紧张情绪"
        case .hate: return "厌恶表达、反感情绪"
        case .surprise: return "惊讶表达、意外情绪"
        case .tear: return "哭泣表达、深度悲伤"
        case .conniving: return "绿茶风格、心机表达"
        case .comfort: return "安慰鼓励、温暖支持"
        case .radio: return "电台主播、磁性声音"
        case .loveyDovey: return "撒娇卖萌、可爱表达"
        case .tsundere: return "傲娇风格、反差萌"
        case .charming: return "娇媚魅惑、温柔诱人"
        case .yoga: return "瑜伽冥想、平静放松"
        case .storytelling: return "故事讲述、生动演绎"
        }
    }
}

// MARK: - TTS状态枚举
enum TTSState: Equatable {
    case idle
    case connecting
    case connected
    case synthesizing
    case playing
    case paused
    case error(String)

    static func == (lhs: TTSState, rhs: TTSState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle),
             (.connecting, .connecting),
             (.connected, .connected),
             (.synthesizing, .synthesizing),
             (.playing, .playing),
             (.paused, .paused):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

// MARK: - TTS请求结构 (基于demo格式)
struct TTSRequest: Codable {
    let app: AppInfo
    let user: UserInfo
    let audio: AudioInfo
    let request: RequestInfo

    struct AppInfo: Codable {
        let appid: String
        let token: String
        let cluster: String
    }

    struct UserInfo: Codable {
        let uid: String
    }

    struct AudioInfo: Codable {
        let voice_type: String
        let encoding: String
        let rate: Int  // 音频采样率，修复重音问题
    }

    struct RequestInfo: Codable {
        let reqid: String
        let text: String
        let operation: String
        let extra_param: String
        let with_timestamp: String
    }
}

// MARK: - TTS服务管理类
@MainActor
class TTSService: NSObject, ObservableObject {
    static let shared = TTSService()

    // MARK: - 配置信息
    private let appId = "1771796339"
    private let accessToken = "MduxvCjM28XnWnXS_a2_NAedHCJ9649D"
    private let wsEndpoint = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"

    // MARK: - 发布属性
    @Published var currentState: TTSState = .idle
    @Published var isPlaying = false
    @Published var currentText = ""
    @Published var playbackProgress: Double = 0.0
    @Published var errorMessage: String?
    @Published var currentEmotion: CanCanEmotion = .pleased // 当前情感状态

    // MARK: - 字音同步相关属性
    @Published var currentPlayingText = "" // 当前正在播放的文本
    @Published var currentPlayingChunk = "" // 当前正在播放的文本片段
    @Published var totalTextChunks: [String] = [] // 所有文本片段
    @Published var currentChunkIndex = 0 // 当前播放的片段索引

    // MARK: - 私有属性
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private var audioPlayer: AVAudioPlayer?
    private var audioData = Data()
    private var currentRequestId: String?

    // 流式播放相关
    private var audioChunks: [Data] = []
    private var isStreamingStarted = false
    private var streamingTimer: Timer?

    // 连接复用相关
    private var isWebSocketConnected = false
    private var connectionRetryCount = 0
    private let maxRetryCount = 3
    private var heartbeatTimer: Timer?

    // 并发控制
    private var isConnecting = false
    private let connectionLock = NSLock()

    // 通知控制
    private var hasNotifiedDataReady = false

    // 网络监控
    private var networkMonitor: NWPathMonitor?
    private var isNetworkAvailable = true

    // MARK: - 音频设置 (擎苍2.0配置)
    private let defaultVoice = "zh_female_wanwanxiaohe_moon_bigtts"     //湾湾小何
    private let audioEncoding = "wav"

    // MARK: - 灿灿2.0情感配置
    /// 灿灿2.0语音情感系统配置
    /// 模型: zh_female_cancan_mars_bigtts
    /// 支持22种情感/风格的智能语音合成
    private struct CanCanConfig {
        static let emotionModelVersion = "zh_female_cancan_mars_bigtts"
        static let defaultModelVersion = "zh_female_wanwanxiaohe_moon_bigtts" // 湾湾小何
        static let supportedEmotions = CanCanEmotion.allCases
        static let defaultEmotion = CanCanEmotion.pleased

        /// 情感参数映射 (预留给未来实现)
        /// 将在后续版本中实现情感参数的具体配置
        static func getEmotionParameters(for emotion: CanCanEmotion) -> [String: Any] {
            // TODO: 实现具体的情感参数配置
            // 这里将配置灿灿2.0的情感参数，如音调、语速、情感强度等
            return [
                "emotion": emotion.rawValue,
                "emotion_description": emotion.description,
                "use_case": emotion.useCase,
                "emotion_model": emotionModelVersion,
                "default_model": defaultModelVersion
            ]
        }
    }

    override init() {
        super.init()
        setupAudioSession()
        setupURLSession()
        startNetworkMonitoring()
        print("✅ TTSService初始化完成")
    }

    // MARK: - 音频会话设置 (优化：统一配置，避免冲突)
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            // 🔧 修复重音问题：统一使用.playback模式和.spokenAudio，专为语音播放优化
            try audioSession.setCategory(.playback, mode: .spokenAudio, options: [.defaultToSpeaker, .allowBluetooth])

            // 🔧 修复重音问题：统一采样率为24000Hz，避免重采样
            try audioSession.setPreferredSampleRate(24000)

            // 🔧 修复音质问题：适中的缓冲延迟，避免音频不稳定
            try audioSession.setPreferredIOBufferDuration(0.02) // 20ms，平衡延迟和稳定性

            try audioSession.setActive(true)
            print("✅ 音频会话统一配置成功 (采样率: 24000Hz, 缓冲: 20ms)")
        } catch {
            print("❌ 音频会话配置失败: \(error)")
            errorMessage = "音频会话设置失败: \(error.localizedDescription)"
        }
    }

    // MARK: - URL会话设置
    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        urlSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }

    // MARK: - 公共接口

    /// 合成文本并返回音频数据（用于流式播放）
    /// - Parameters:
    ///   - text: 要合成的文本
    ///   - emotion: 情感音色
    /// - Returns: 音频数据，如果合成失败返回nil
    func synthesizeTextToData(text: String, emotion: CanCanEmotion) async -> Data? {
        return await synthesizeTextToDataWithReqId(text: text, emotion: emotion, reqId: UUID().uuidString)
    }

    /// 合成文本并返回音频数据（支持自定义reqid，用于并行处理）
    /// - Parameters:
    ///   - text: 要合成的文本
    ///   - emotion: 情感音色
    ///   - reqId: 请求标识符，用于跟踪并行请求
    /// - Returns: 音频数据，如果合成失败返回nil
    func synthesizeTextToDataWithReqId(text: String, emotion: CanCanEmotion, reqId: String) async -> Data? {
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("❌ 合成文本为空")
            return nil
        }

        print("🎤 合成文本到数据: \(text.prefix(30))...")
        print("🎭 使用情感: \(emotion.description) (\(emotion.rawValue))")
        print("🆔 使用reqid: \(reqId)")

        // 使用信号量来等待合成完成，防止continuation被多次resume
        return await withCheckedContinuation { continuation in
            Task {
                // 保存原始状态
                let originalEmotion = currentEmotion
                let originalText = currentText
                let originalAudioData = audioData

                // 设置新的合成参数
                currentEmotion = emotion
                currentText = text
                audioData = Data()
                hasNotifiedDataReady = false // 重置通知标志

                // 使用标志位防止多次resume
                var hasResumed = false
                let resumeLock = NSLock()

                func safeResume(with result: Data?) {
                    resumeLock.lock()
                    defer { resumeLock.unlock() }

                    if !hasResumed {
                        hasResumed = true
                        continuation.resume(returning: result)
                    }
                }

                // 创建一个临时的播放完成监听器
                var observer: NSObjectProtocol?
                observer = NotificationCenter.default.addObserver(
                    forName: NSNotification.Name("TTSAudioDataReady"),
                    object: nil,
                    queue: .main
                ) { [weak self] _ in
                    guard let self = self else {
                        safeResume(with: nil)
                        return
                    }

                    let resultData = self.audioData

                    // 恢复原始状态
                    self.currentEmotion = originalEmotion
                    self.currentText = originalText
                    self.audioData = originalAudioData

                    // 移除观察者
                    if let observer = observer {
                        NotificationCenter.default.removeObserver(observer)
                    }

                    if resultData.isEmpty {
                        print("❌ 文本合成失败，无音频数据")
                        safeResume(with: nil)
                    } else {
                        print("✅ 文本合成完成，音频数据大小: \(resultData.count) 字节")
                        safeResume(with: resultData)
                    }
                }

                // 执行合成（不播放），使用自定义reqid
                await self.synthesizeOnlyWithReqId(text: text, reqId: reqId)

                // 设置超时
                DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                    resumeLock.lock()
                    let shouldTimeout = !hasResumed
                    resumeLock.unlock()

                    if shouldTimeout {
                        if let observer = observer {
                            NotificationCenter.default.removeObserver(observer)
                        }
                        print("❌ 文本合成超时")
                        safeResume(with: nil)
                    }
                }
            }
        }
    }

    /// 仅合成音频，不播放
    private func synthesizeOnly(text: String) async {
        // 确保连接并合成
        await ensureConnectionAndSynthesize(text: text)

        // 注意：这里不调用播放方法，只等待音频数据准备完成
        // 当音频数据准备完成时，会通过通知机制通知调用者
    }

    /// 仅合成音频，不播放（支持自定义reqid）
    private func synthesizeOnlyWithReqId(text: String, reqId: String) async {
        // 确保连接并合成，使用自定义reqid
        await ensureConnectionAndSynthesizeWithReqId(text: text, reqId: reqId)

        // 注意：这里不调用播放方法，只等待音频数据准备完成
        // 当音频数据准备完成时，会通过通知机制通知调用者
    }

    /// 合成并播放文本 (优化：复用WebSocket连接)
    /// - Parameters:
    ///   - text: 要合成的文本
    ///   - emotion: 情感音色 (可选，默认使用当前情感)
    func synthesizeAndPlay(text: String, emotion: CanCanEmotion? = nil) {
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("❌ 合成文本为空")
            return
        }

        // 检查网络连接
        guard isNetworkAvailable else {
            print("❌ 网络连接不可用，无法进行TTS合成")
            errorMessage = "网络连接不可用"
            currentState = .error("网络错误")
            return
        }

        // 更新情感状态 (如果指定了新情感)
        if let newEmotion = emotion {
            currentEmotion = newEmotion
            print("🎭 TTSService切换情感音色: \(newEmotion.description) (\(newEmotion.rawValue))")
            print("🎵 音色详细信息: 音色ID=\(defaultVoice), 情感=\(newEmotion.rawValue)")
        } else {
            print("🎭 TTSService使用当前情感音色: \(currentEmotion.description) (\(currentEmotion.rawValue))")
            print("🎵 音色详细信息: 音色ID=\(defaultVoice), 情感=\(currentEmotion.rawValue)")
        }

        // 只停止音频播放，保持WebSocket连接
        stopAudioOnly()

        currentText = text
        errorMessage = nil
        currentState = .connecting
        hasNotifiedDataReady = false // 重置通知标志

        // 🎯 设置字音同步相关信息
        currentPlayingText = text
        totalTextChunks = splitTextIntoDisplayChunks(text)
        currentChunkIndex = 0
        currentPlayingChunk = totalTextChunks.first ?? ""

        print("🎤 开始TTS合成: \(text)")
        print("🎵 使用音色: \(defaultVoice) (湾湾小何 - 趣味方言)")
        print("🎭 当前情感: \(currentEmotion.description) (\(currentEmotion.rawValue))")
        print("📦 压缩方式: 无压缩")
        print("🤖 灿灿2.0模型: \(CanCanConfig.emotionModelVersion)")
        print("📝 文本已分割为 \(totalTextChunks.count) 个显示片段")

        Task {
            await ensureConnectionAndSynthesize(text: text)
        }
    }

    /// 只停止音频播放，保持连接
    private func stopAudioOnly() {
        audioPlayer?.stop()
        audioPlayer = nil

        // 清理流式播放状态
        streamingTimer?.invalidate()
        streamingTimer = nil
        audioChunks.removeAll()
        isStreamingStarted = false

        isPlaying = false
        playbackProgress = 0.0
        audioData = Data()
        currentRequestId = nil

        print("⏹️ 停止TTS播放 (保持连接，当前情感: \(currentEmotion.description))")
    }

    /// 停止播放 (AI整句回复结束时调用)
    func stopPlaying() {
        audioPlayer?.stop()
        audioPlayer = nil
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil

        // 清理流式播放状态
        streamingTimer?.invalidate()
        streamingTimer = nil
        audioChunks.removeAll()
        isStreamingStarted = false

        isPlaying = false
        playbackProgress = 0.0
        currentText = ""
        currentState = .idle
        audioData = Data()
        currentRequestId = nil

        print("⏹️ 停止TTS播放 (AI回复结束，情感: \(currentEmotion.description))")
    }

    /// 暂停播放
    func pausePlaying() {
        guard isPlaying else { return }
        audioPlayer?.pause()
        isPlaying = false
        currentState = .paused
        print("⏸️ 暂停TTS播放")
    }

    /// 继续播放
    func resumePlaying() {
        guard currentState == .paused, let player = audioPlayer else { return }
        player.play()
        isPlaying = true
        currentState = .playing
        print("▶️ 继续TTS播放")
    }

    // MARK: - 灿灿2.0情感控制接口

    /// 设置当前情感音色
    /// - Parameter emotion: 要设置的情感音色
    func setEmotion(_ emotion: CanCanEmotion) {
        currentEmotion = emotion
        print("🎭 设置情感音色: \(emotion.description) (\(emotion.rawValue))")
        print("📝 适用场景: \(emotion.useCase)")
    }

    /// 获取当前情感音色
    /// - Returns: 当前的情感音色
    func getCurrentEmotion() -> CanCanEmotion {
        return currentEmotion
    }

    /// 获取所有支持的情感音色
    /// - Returns: 所有支持的情感音色数组
    func getSupportedEmotions() -> [CanCanEmotion] {
        return CanCanConfig.supportedEmotions
    }

    /// 根据文本内容智能推荐情感音色 (预留功能)
    /// - Parameter text: 要分析的文本
    /// - Returns: 推荐的情感音色
    func recommendEmotion(for text: String) -> CanCanEmotion {
        // TODO: 实现基于文本内容的情感分析和推荐
        // 这里将在后续版本中实现AI情感分析功能
        // 根据文本内容的情感倾向自动推荐合适的音色

        // 简单的关键词匹配示例 (后续可扩展为AI分析)
        let lowercaseText = text.lowercased()

        if lowercaseText.contains("开心") || lowercaseText.contains("高兴") || lowercaseText.contains("哈哈") {
            return .happy
        } else if lowercaseText.contains("难过") || lowercaseText.contains("伤心") || lowercaseText.contains("失望") {
            return .sad
        } else if lowercaseText.contains("生气") || lowercaseText.contains("愤怒") || lowercaseText.contains("气死") {
            return .angry
        } else if lowercaseText.contains("害怕") || lowercaseText.contains("恐怖") || lowercaseText.contains("紧张") {
            return .scare
        } else if lowercaseText.contains("惊讶") || lowercaseText.contains("意外") || lowercaseText.contains("没想到") {
            return .surprise
        } else if lowercaseText.contains("哭") || lowercaseText.contains("眼泪") || lowercaseText.contains("痛苦") {
            return .tear
        } else if lowercaseText.contains("抱歉") || lowercaseText.contains("对不起") || lowercaseText.contains("不好意思") {
            return .sorry
        } else if lowercaseText.contains("撒娇") || lowercaseText.contains("可爱") || lowercaseText.contains("萌") {
            return .loveyDovey
        } else if lowercaseText.contains("专业") || lowercaseText.contains("正式") || lowercaseText.contains("商务") {
            return .professional
        } else if lowercaseText.contains("故事") || lowercaseText.contains("讲述") || lowercaseText.contains("从前") {
            return .storytelling
        } else {
            return .pleased // 默认愉悦音色
        }
    }

    // MARK: - 确保连接并合成 (优化版本)
    private func ensureConnectionAndSynthesize(text: String) async {
        await ensureConnectionAndSynthesizeWithReqId(text: text, reqId: UUID().uuidString)
    }

    /// 确保连接并合成（支持自定义reqid）
    private func ensureConnectionAndSynthesizeWithReqId(text: String, reqId: String) async {
        // 使用锁防止并发连接
        connectionLock.lock()
        let shouldConnect = !isConnecting && (!isWebSocketConnected || webSocketTask?.state != .running)
        if shouldConnect {
            isConnecting = true
        }
        connectionLock.unlock()

        // 检查现有连接是否可用
        if !shouldConnect && isWebSocketConnected && webSocketTask?.state == .running {
            print("🔄 复用现有WebSocket连接")
            await sendTTSRequestWithReqId(text: text, reqId: reqId)
            return
        }

        // 如果其他任务正在连接，等待连接完成
        if !shouldConnect {
            print("⏳ 等待其他连接任务完成...")
            while isConnecting {
                try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
            }

            // 连接完成后重新检查
            if isWebSocketConnected && webSocketTask?.state == .running {
                print("🔄 使用已建立的WebSocket连接")
                await sendTTSRequestWithReqId(text: text, reqId: reqId)
                return
            }
        }

        // 需要建立新连接
        if shouldConnect {
            print("🔗 建立新的WebSocket连接")
            await connectAndSynthesizeWithReqId(text: text, reqId: reqId)

            connectionLock.lock()
            isConnecting = false
            connectionLock.unlock()
        }
    }

    // MARK: - WebSocket连接和合成 (基于demo实现)
    private func connectAndSynthesize(text: String) async {
        await connectAndSynthesizeWithReqId(text: text, reqId: UUID().uuidString)
    }

    /// WebSocket连接和合成（支持自定义reqid）
    private func connectAndSynthesizeWithReqId(text: String, reqId: String) async {
        guard let url = URL(string: wsEndpoint) else {
            await MainActor.run {
                errorMessage = "WebSocket URL无效"
                currentState = .error("WebSocket URL无效")
            }
            return
        }

        // 创建WebSocket连接 (按照API文档格式)
        var request = URLRequest(url: url)
        request.setValue("Bearer; \(accessToken)", forHTTPHeaderField: "Authorization")

        webSocketTask = urlSession?.webSocketTask(with: request)
        webSocketTask?.resume()

        await MainActor.run {
            currentState = .connected
        }

        // 标记连接状态
        isWebSocketConnected = true
        connectionRetryCount = 0

        print("🔗 WebSocket连接已建立")

        // 开始接收消息
        startReceivingMessages()

        // 启动心跳机制
        startHeartbeat()

        // 发送TTS请求
        await sendTTSRequestWithReqId(text: text, reqId: reqId)
    }

    private func sendTTSRequest(text: String) async {
        await sendTTSRequestWithReqId(text: text, reqId: UUID().uuidString)
    }

    /// 发送TTS请求（支持自定义reqid）
    private func sendTTSRequestWithReqId(text: String, reqId: String) async {
        currentRequestId = reqId

        // 🔧 修复消息过大问题：检查文本字节数而不是字符数
        let textData = text.data(using: .utf8) ?? Data()
        let maxTextBytes = 50000 // 限制单次请求的文本字节数，约50KB，远小于1MB限制

        if textData.count > maxTextBytes {
            print("⚠️ 文本过长(\(text.count)字符, \(textData.count)字节)，分段处理")
            await sendLongTextInChunks(text: text, requestId: reqId)
            return
        }

        print("📝 发送TTS请求: 文本=\(text.count)字符, 字节=\(textData.count)")
        print("📝 文本内容: \(text.prefix(50))...")

        // 构建请求 (按照API文档格式)
        let ttsRequest = TTSRequest(
            app: TTSRequest.AppInfo(
                appid: appId,
                token: accessToken,  // Fake token，可传任意非空字符串
                cluster: "volcano_tts"  // 固定为volcano_tts
            ),
            user: TTSRequest.UserInfo(
                uid: UUID().uuidString
            ),
            audio: TTSRequest.AudioInfo(
                voice_type: defaultVoice,  // 中文女声
                encoding: audioEncoding,
                rate: 24000  // 🔧 修复重音问题：明确指定24000Hz采样率
            ),
            request: TTSRequest.RequestInfo(
                reqid: reqId,
                text: text,
                operation: "submit",  // 流式操作
                extra_param: "{\"disable_markdown_filter\": false}",
                with_timestamp: "1"
            )
        )

        do {
            let jsonData = try JSONEncoder().encode(ttsRequest)
            let message = createFullClientRequestMessage(payload: jsonData)

            // 检查消息大小
            let messageSize = message.count
            print("📦 创建消息: 大小=\(messageSize)字节, Payload=\(jsonData.count)字节, 压缩=无")

            if messageSize > 800_000 { // 800KB限制，留出缓冲
                print("❌ 消息过大(\(messageSize)字节)，跳过发送")
                await MainActor.run {
                    errorMessage = "消息过大，无法发送"
                    currentState = .error("消息过大")
                }
                return
            }

            try await webSocketTask?.send(.data(message))

            await MainActor.run {
                currentState = .synthesizing
            }

            print("📤 发送TTS请求: \(reqId)")
            print("🎵 音色: \(defaultVoice) (湾湾小何 - 趣味方言)")
            print("🎭 情感音色: \(currentEmotion.description) (\(currentEmotion.rawValue))")
            print("📝 文本: \(text)")
            print("🤖 灿灿2.0模型: \(CanCanConfig.emotionModelVersion)")
            print("🔧 修复参数: 采样率=24000Hz, 编码=\(audioEncoding), 操作=submit")
        } catch {
            await MainActor.run {
                errorMessage = "发送TTS请求失败: \(error.localizedDescription)"
                currentState = .error("发送请求失败")
            }
            print("❌ 发送TTS请求失败: \(error)")
        }
    }

    /// 分段处理长文本 - 按字节数智能分割
    private func sendLongTextInChunks(text: String, requestId: String) async {
        let maxChunkBytes = 30000 // 每个片段最大30KB字节数
        let chunks = splitTextByBytes(text: text, maxBytes: maxChunkBytes)

        print("📝 文本已分割为 \(chunks.count) 个片段")
        for (index, chunk) in chunks.enumerated() {
            let chunkBytes = chunk.data(using: .utf8)?.count ?? 0
            print("📝 片段 \(index + 1): 长度=\(chunk.count)字符, 字节=\(chunkBytes), 内容=\(chunk.prefix(50))...")
        }

        for (index, chunk) in chunks.enumerated() {
            let chunkRequestId = "\(requestId)-\(index)"
            print("📤 并行合成片段 \(index + 1)/\(chunks.count): \(chunk.prefix(50))...")

            // 为每个片段创建单独的请求
            await sendSingleChunk(chunk, requestId: chunkRequestId)

            // 片段间稍作延迟，避免服务器压力
            try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
        }
    }

    /// 按字节数智能分割文本，尽量在句子边界分割
    private func splitTextByBytes(text: String, maxBytes: Int) -> [String] {
        var chunks: [String] = []
        var currentChunk = ""

        // 按句子分割
        let sentences = text.components(separatedBy: CharacterSet(charactersIn: "。！？\n"))

        for sentence in sentences {
            let testChunk = currentChunk.isEmpty ? sentence : currentChunk + sentence
            let testBytes = testChunk.data(using: .utf8)?.count ?? 0

            if testBytes <= maxBytes {
                currentChunk = testChunk
            } else {
                // 当前句子会超出限制
                if !currentChunk.isEmpty {
                    chunks.append(currentChunk)
                    currentChunk = sentence
                } else {
                    // 单个句子就超出限制，强制按字符分割
                    let charChunks = sentence.chunked(into: maxBytes / 3) // 保守估计中文字符占3字节
                    chunks.append(contentsOf: charChunks)
                }
            }
        }

        if !currentChunk.isEmpty {
            chunks.append(currentChunk)
        }

        return chunks.isEmpty ? [text] : chunks
    }

    /// 将文本分割成适合显示的片段（用于字音同步显示）
    private func splitTextIntoDisplayChunks(_ text: String) -> [String] {
        // 按句子和短语分割，适合逐句显示
        let delimiters = CharacterSet(charactersIn: "。！？，、；：\n.!?,;:")
        let chunks = text.components(separatedBy: delimiters)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }

        return chunks.isEmpty ? [text] : chunks
    }

    /// 发送单个文本片段
    private func sendSingleChunk(_ text: String, requestId: String) async {
        let ttsRequest = TTSRequest(
            app: TTSRequest.AppInfo(
                appid: appId,
                token: accessToken,
                cluster: "volcano_tts"
            ),
            user: TTSRequest.UserInfo(
                uid: UUID().uuidString
            ),
            audio: TTSRequest.AudioInfo(
                voice_type: defaultVoice,
                encoding: audioEncoding,
                rate: 24000  // 🔧 修复重音问题：明确指定24000Hz采样率
            ),
            request: TTSRequest.RequestInfo(
                reqid: requestId,
                text: text,
                operation: "submit",
                extra_param: "{\"disable_markdown_filter\": false}",
                with_timestamp: "1"
            )
        )

        do {
            let jsonData = try JSONEncoder().encode(ttsRequest)
            let message = createFullClientRequestMessage(payload: jsonData)
            try await webSocketTask?.send(.data(message))
        } catch {
            print("❌ 发送片段失败: \(error)")
        }
    }


    // MARK: - 消息创建 (按照API文档格式，无压缩)
    private func createFullClientRequestMessage(payload: Data) -> Data {
        var message = Data()

        // 基础头部 (按照API文档格式，修复协议头部)
        message.append(0x14) // 🔧 修复重音问题：version=1, headerSize=4 (16字节头部)
        message.append(0x10) // type=FullClientRequest(1), flag=NoSeq(0)
        message.append(0x10) // serialization=JSON(1), compression=None(0) - 无压缩
        message.append(0x00) // 填充字节

        // Payload大小 (4字节，大端序)
        let payloadSize = UInt32(payload.count).bigEndian
        message.append(contentsOf: withUnsafeBytes(of: payloadSize) { Array($0) })

        // Payload数据
        message.append(payload)

        print("📦 创建消息: 大小=\(message.count)字节, Payload=\(payload.count)字节, 压缩=无")

        return message
    }

    // MARK: - 消息处理 (基于demo逻辑)
    private func startReceivingMessages() {
        guard let webSocketTask = webSocketTask else { return }

        webSocketTask.receive { [weak self] result in
            switch result {
            case .success(let message):
                Task {
                    await self?.handleWebSocketMessage(message)
                    self?.startReceivingMessages() // 继续接收下一条消息
                }
            case .failure(let error):
                Task {
                    await self?.handleWebSocketError(error)
                }
            }
        }
    }

    private func handleWebSocketMessage(_ message: URLSessionWebSocketTask.Message) async {
        switch message {
        case .data(let data):
            await processMessageData(data)
        case .string(let string):
            print("📥 收到文本消息: \(string)")
        @unknown default:
            print("❓ 收到未知类型消息")
        }
    }

    private func processMessageData(_ data: Data) async {
        // 解析二进制消息 (按照API文档格式)
        guard data.count >= 8 else {
            print("❌ 消息数据太短: \(data.count) 字节")
            return
        }

        let messageType = (data[1] >> 4) & 0b1111
        print("📥 收到消息类型: \(messageType)")

        switch messageType {
        case 0b1011: // AudioOnlyServer (0x000B)
            await handleAudioData(data)
        case 0b1100: // FrontEndResultServer (0x000C)
            await handleResultData(data)
        case 0b1111: // Error (0x000F)
            await handleErrorData(data)
        default:
            print("📥 未处理的消息类型: \(messageType)")
        }
    }

    private func handleAudioData(_ data: Data) async {
        // 提取音频数据（按照API文档格式）
        let headerSize = 4 * Int(data[0] & 0b1111)
        guard data.count >= headerSize else {
            print("❌ 音频数据格式错误: headerSize=\(headerSize), dataSize=\(data.count)")
            return
        }

        let flag = data[1] & 0b1111
        var offset = headerSize

        // 如果有sequence字段，需要读取它
        var sequence: Int32? = nil
        if flag == 0b01 || flag == 0b11 { // PositiveSeq or NegativeSeq
            guard data.count >= offset + 4 else {
                print("❌ 音频数据不完整: 无法读取sequence")
                return
            }
            let sequenceData = data.subdata(in: offset..<offset+4)
            sequence = sequenceData.withUnsafeBytes { $0.load(as: Int32.self).bigEndian }
            offset += 4
            print("📊 音频序列号: \(sequence!)")
        }

        // 读取payload大小 (大端序)
        guard data.count >= offset + 4 else {
            print("❌ 音频数据不完整: 无法读取payload大小")
            return
        }

        let payloadSizeData = data.subdata(in: offset..<offset+4)
        let payloadSize = payloadSizeData.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
        offset += 4

        guard data.count >= offset + Int(payloadSize) else {
            print("❌ 音频数据不完整: 期望\(payloadSize)字节，实际\(data.count - offset)字节")
            return
        }

        // 提取音频数据
        let audioChunk = data.subdata(in: offset..<offset+Int(payloadSize))
        audioData.append(audioChunk)
        audioChunks.append(audioChunk)

        print("🎵 收到音频数据: \(audioChunk.count) 字节，总计: \(audioData.count) 字节")

        // 🔧 修复重音问题：增加流式播放缓冲，确保音频头部完整
        if !isStreamingStarted && audioData.count > 16384 { // 16KB缓冲后开始播放，确保WAV头部完整
            print("🎵 开始流式播放 (缓冲: \(audioData.count) 字节)")
            isStreamingStarted = true
            await startStreamingPlayback()
        }

        // 🔧 修复重音问题：统一音频结束条件判断，只使用sequence < 0
        let isLastAudioChunk = (sequence != nil && sequence! < 0) || (flag == 0b11)

        if isLastAudioChunk {
            print("🎵 音频数据接收完成 (sequence=\(sequence?.description ?? "nil"), flag=\(flag))，流式播放模式")

            // 发送音频数据准备完成通知（只发送一次）
            await MainActor.run {
                if !hasNotifiedDataReady {
                    hasNotifiedDataReady = true
                    NotificationCenter.default.post(name: NSNotification.Name("TTSAudioDataReady"), object: nil)
                }
            }

            if !isStreamingStarted {
                // 如果还没开始流式播放，直接播放所有数据
                debugAudioData()
                await playAudioData()
            }
        }
    }

    // MARK: - 音频数据调试 (增强版本，用于验证修复效果)
    private func debugAudioData() {
        print("🔍 音频数据调试信息:")
        print("   - 总大小: \(audioData.count) 字节")

        if audioData.count >= 44 {
            // 检查WAV文件头
            let header = audioData.prefix(44)
            let riffSignature = String(data: header.prefix(4), encoding: .ascii) ?? "未知"
            let waveSignature = String(data: header.dropFirst(8).prefix(4), encoding: .ascii) ?? "未知"

            print("   - RIFF签名: \(riffSignature)")
            print("   - WAVE签名: \(waveSignature)")

            if riffSignature == "RIFF" && waveSignature == "WAVE" {
                print("   - ✅ 检测到有效的WAV文件头")

                // 🔧 增强调试：解析WAV头部详细信息
                if audioData.count >= 44 {
                    let sampleRateBytes = header.dropFirst(24).prefix(4)
                    let sampleRate = sampleRateBytes.withUnsafeBytes { $0.load(as: UInt32.self) }

                    let channelsBytes = header.dropFirst(22).prefix(2)
                    let channels = channelsBytes.withUnsafeBytes { $0.load(as: UInt16.self) }

                    let bitsPerSampleBytes = header.dropFirst(34).prefix(2)
                    let bitsPerSample = bitsPerSampleBytes.withUnsafeBytes { $0.load(as: UInt16.self) }

                    print("   - 📊 WAV格式详情:")
                    print("     * 采样率: \(sampleRate) Hz (期望: 24000 Hz)")
                    print("     * 声道数: \(channels) (期望: 1)")
                    print("     * 位深度: \(bitsPerSample) bits (期望: 16)")

                    // 验证采样率是否匹配
                    if sampleRate == 24000 {
                        print("     * ✅ 采样率匹配，重音问题应已修复")
                    } else {
                        print("     * ❌ 采样率不匹配，可能仍有重音问题")
                    }
                }
            } else {
                print("   - ❌ 无效的WAV文件头")
            }
        } else {
            print("   - ❌ 音频数据太小，无法包含完整的WAV头")
        }
    }

    private func handleResultData(_ data: Data) async {
        print("📊 收到结果数据")
    }

    private func handleErrorData(_ data: Data) async {
        // 解析错误信息
        let headerSize = 4 * Int(data[0] & 0b1111)
        if data.count > headerSize + 8 {
            let errorCodeData = data.subdata(in: headerSize..<headerSize+4)
            let errorCode = errorCodeData.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }

            let payloadSizeData = data.subdata(in: headerSize+4..<headerSize+8)
            let payloadSize = payloadSizeData.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }

            if data.count >= headerSize + 8 + Int(payloadSize) {
                let errorPayload = data.subdata(in: headerSize+8..<headerSize+8+Int(payloadSize))
                let errorMessage = String(data: errorPayload, encoding: .utf8) ?? "未知错误"

                await MainActor.run {
                    self.errorMessage = "TTS服务错误 (\(errorCode)): \(errorMessage)"
                    self.currentState = .error("服务器错误")
                }
                print("❌ TTS服务错误 (\(errorCode)): \(errorMessage)")
                return
            }
        }

        await MainActor.run {
            errorMessage = "TTS服务返回错误"
            currentState = .error("服务器错误")
        }
        print("❌ 收到错误数据")
    }

    private func handleWebSocketError(_ error: Error) async {
        isWebSocketConnected = false

        print("❌ WebSocket错误: \(error)")

        // 检查是否是网络错误，如果是则尝试重连
        let nsError = error as NSError
        if nsError.domain == NSURLErrorDomain {
            print("🔄 检测到网络错误，尝试重连...")
            await retryConnection()
        } else {
            await MainActor.run {
                errorMessage = "WebSocket连接错误: \(error.localizedDescription)"
                currentState = .error("连接错误")
            }
        }
    }

    // MARK: - 流式音频播放 (修复重音问题：使用统一音频会话配置)
    private func startStreamingPlayback() async {
        guard !audioData.isEmpty else {
            print("❌ 没有音频数据可播放")
            return
        }

        do {
            print("🎵 开始流式播放，当前数据: \(audioData.count) 字节")

            // 🔧 修复重音问题：不重复配置音频会话，使用统一配置
            let audioSession = AVAudioSession.sharedInstance()
            if !audioSession.isOtherAudioPlaying {
                try? audioSession.setActive(true)
            }

            let player = try AVAudioPlayer(data: audioData)
            player.delegate = self
            player.volume = 0.85  // 🔧 修复音质：适中音量
            player.enableRate = false  // 🔧 修复重音：禁用播放速率控制

            await MainActor.run {
                audioPlayer = player
                isPlaying = true
                currentState = .playing
            }

            guard player.prepareToPlay() && player.play() else {
                print("❌ 流式播放启动失败")
                await MainActor.run {
                    isPlaying = false
                    currentState = .error("播放失败")
                }
                return
            }

            print("🔊 流式播放开始 (音色: \(defaultVoice), 情感: \(currentEmotion.description))，当前时长: \(player.duration) 秒")

        } catch {
            print("❌ 流式播放失败: \(error)")
            await MainActor.run {
                errorMessage = "流式播放失败: \(error.localizedDescription)"
                currentState = .error("播放失败")
            }
        }
    }

    // MARK: - 音频播放 (修复重音问题：避免重复配置音频会话)
    private func playAudioData() async {
        guard !audioData.isEmpty else {
            print("❌ 没有音频数据可播放")
            await MainActor.run {
                currentState = .error("无音频数据")
            }
            return
        }

        do {
            print("🎵 准备播放音频数据，大小: \(audioData.count) 字节")

            // 🔧 修复重音问题：不重复配置音频会话，使用初始化时的统一配置
            // 只确保音频会话处于活跃状态
            let audioSession = AVAudioSession.sharedInstance()
            if !audioSession.isOtherAudioPlaying {
                try? audioSession.setActive(true)
            }

            print("✅ 使用统一音频会话配置 (采样率: \(audioSession.sampleRate)Hz)")

            let player = try AVAudioPlayer(data: audioData)
            player.delegate = self
            player.volume = 0.85  // 🔧 修复音质：适中音量，避免失真
            player.enableRate = false  // 🔧 修复重音：禁用播放速率控制，避免音调变化

            // 🔧 修复音质：预先准备播放器
            let prepareResult = player.prepareToPlay()
            print("🎵 音频播放器准备结果: \(prepareResult)")

            // 检查音频格式信息
            print("🎵 音频格式信息: 时长=\(player.duration)秒, 声道=\(player.numberOfChannels)")
            print("🎵 音频格式详情: 采样率=\(player.format.sampleRate)Hz, 通道数=\(player.format.channelCount)")

            await MainActor.run {
                audioPlayer = player
                isPlaying = true
                currentState = .playing
            }

            if !prepareResult {
                print("❌ 音频播放器准备失败")
                await MainActor.run {
                    errorMessage = "音频播放器准备失败"
                    currentState = .error("播放失败")
                    isPlaying = false
                }
                return
            }

            // 开始播放
            let playResult = player.play()
            print("🎵 音频播放启动结果: \(playResult)")

            if !playResult {
                print("❌ 音频播放启动失败，可能的原因:")
                print("   - 音频格式不支持")
                print("   - 音频数据损坏")
                print("   - 音频会话配置问题")

                await MainActor.run {
                    errorMessage = "音频播放启动失败"
                    currentState = .error("播放失败")
                    isPlaying = false
                }
                return
            }

            print("🔊 开始播放TTS音频 (音色: \(defaultVoice), 情感: \(currentEmotion.description))，时长: \(player.duration) 秒，音量: \(player.volume)")
            print("🎵 音频格式: 采样率=\(player.format.sampleRate), 通道数=\(player.format.channelCount)")
            print("🎵 播放状态: isPlaying=\(player.isPlaying)")

        } catch {
            await MainActor.run {
                errorMessage = "音频播放失败: \(error.localizedDescription)"
                currentState = .error("播放失败")
                isPlaying = false
            }
            print("❌ 音频播放失败: \(error)")
            print("❌ 错误详情: \(error)")
        }
    }

    // MARK: - 心跳机制
    private func startHeartbeat() {
        heartbeatTimer?.invalidate()
        heartbeatTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.sendHeartbeat()
        }
    }

    private func sendHeartbeat() {
        guard let webSocketTask = webSocketTask, webSocketTask.state == .running else {
            print("💔 心跳检测：连接已断开")
            isWebSocketConnected = false
            // 尝试重连
            Task {
                await retryConnection()
            }
            return
        }

        // 发送ping消息
        webSocketTask.sendPing { [weak self] error in
            if let error = error {
                print("💔 心跳失败: \(error)")
                self?.isWebSocketConnected = false
                // 尝试重连
                Task {
                    await self?.retryConnection()
                }
            } else {
                print("💓 心跳正常")
            }
        }
    }

    // MARK: - 连接重试
    private func retryConnection() async {
        guard connectionRetryCount < maxRetryCount else {
            print("❌ 连接重试次数已达上限")
            await MainActor.run {
                errorMessage = "连接失败，请稍后重试"
                currentState = .error("连接失败")
            }
            return
        }

        connectionRetryCount += 1
        print("🔄 尝试重新连接 (\(connectionRetryCount)/\(maxRetryCount))")

        // 清理旧连接
        await cleanupConnection()

        // 递增延迟重试：2秒、4秒、6秒
        let retryDelay = UInt64(connectionRetryCount * 2_000_000_000)
        try? await Task.sleep(nanoseconds: retryDelay)

        if !currentText.isEmpty {
            await connectAndSynthesize(text: currentText)
        }
    }

    /// 清理连接资源
    private func cleanupConnection() async {
        await MainActor.run {
            heartbeatTimer?.invalidate()
            heartbeatTimer = nil

            webSocketTask?.cancel(with: .goingAway, reason: nil)
            webSocketTask = nil

            isWebSocketConnected = false

            print("🧹 已清理WebSocket连接资源")
        }
    }

    // MARK: - 网络监控
    private func startNetworkMonitoring() {
        networkMonitor = NWPathMonitor()
        networkMonitor?.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                let wasAvailable = self?.isNetworkAvailable ?? true
                self?.isNetworkAvailable = path.status == .satisfied

                if wasAvailable && path.status != .satisfied {
                    print("🌐 网络连接已断开")
                } else if !wasAvailable && path.status == .satisfied {
                    print("🌐 网络连接已恢复")
                    // 网络恢复后重置重试计数
                    self?.connectionRetryCount = 0
                }
            }
        }

        let queue = DispatchQueue(label: "NetworkMonitor")
        networkMonitor?.start(queue: queue)
        print("🌐 网络监控已启动")
    }

    private func stopNetworkMonitoring() {
        networkMonitor?.cancel()
        networkMonitor = nil
        print("🌐 网络监控已停止")
    }

    deinit {
        // 清理资源 - 不能在deinit中调用主线程方法
        audioPlayer?.stop()
        audioPlayer = nil
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        urlSession?.invalidateAndCancel()
        networkMonitor?.cancel()
        networkMonitor = nil
    }
}

// MARK: - URLSessionWebSocketDelegate
extension TTSService: URLSessionWebSocketDelegate {
    nonisolated func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didOpenWithProtocol protocol: String?) {
        print("✅ WebSocket连接已建立")
        Task { @MainActor in
            isWebSocketConnected = true
            connectionRetryCount = 0
        }
    }

    nonisolated func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didCloseWith closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?) {
        print("🔌 WebSocket连接已关闭: \(closeCode)")
        Task { @MainActor in
            isWebSocketConnected = false
            heartbeatTimer?.invalidate()
            heartbeatTimer = nil

            // 如果不是正常关闭且有待处理的文本，尝试重连
            if closeCode != .normalClosure && !currentText.isEmpty {
                Task {
                    await retryConnection()
                }
            }
        }
    }
}

// MARK: - String扩展
extension String {
    /// 将字符串分割成指定长度的片段
    func chunked(into size: Int) -> [String] {
        var chunks: [String] = []
        var currentIndex = startIndex

        while currentIndex < endIndex {
            let nextIndex = index(currentIndex, offsetBy: size, limitedBy: endIndex) ?? endIndex
            let chunk = String(self[currentIndex..<nextIndex])
            chunks.append(chunk)
            currentIndex = nextIndex
        }

        return chunks
    }
}

// MARK: - AVAudioPlayerDelegate
extension TTSService: AVAudioPlayerDelegate {
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            print("✅ TTS音频播放完成 (音色: \(defaultVoice), 情感: \(currentEmotion.description))")

            // 重置播放状态
            isPlaying = false
            playbackProgress = 1.0
            currentState = .idle
            currentText = ""

            // 清理音频数据和流式播放状态
            audioData = Data()
            audioChunks.removeAll()
            isStreamingStarted = false

            // 清理音频播放器
            audioPlayer = nil

            print("🔄 TTS播放状态已重置，准备接受新的语音输入")
        }
    }

    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            errorMessage = "音频解码错误: \(error?.localizedDescription ?? "未知错误")"
            currentState = .error("解码错误")
            isPlaying = false
        }
    }
}