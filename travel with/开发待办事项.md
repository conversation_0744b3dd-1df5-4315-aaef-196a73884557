# Travel With 开发待办事项

## 🚨 高优先级 TODO

### 1. 长期记忆RAG系统优化 (🔥🔥🔥)
**问题描述**：当前的长期记忆检索效果不佳，使用的是简单的文本匹配，需要改进为基于向量相似度的RAG检索。

**当前实现问题**：
- 使用整个prompt进行检索，导致检索不准确
- 缺乏语义理解，只能进行关键词匹配
- 检索结果相关性较低

**期望改进**：
- 实现基于文本向量相似度的RAG检索
- 使用embedding模型将文本转换为向量
- 通过向量相似度计算找到最相关的记忆
- 支持语义级别的相似性匹配

**技术方案**：
```swift
// 需要实现的接口
protocol VectorRAGSystem {
    func embedText(_ text: String) async -> [Float]
    func searchSimilarMemories(_ queryVector: [Float], limit: Int) async -> [Memory]
    func addMemoryWithEmbedding(_ memory: Memory) async
}
```

**文件位置**：
- `travel with/LongTermMemoryManager.swift` - 需要重构
- 新增：`travel with/VectorRAGSystem.swift`

---

### 2. TTS流式播放系统 (✅ 已完成)
**问题描述**：当前TTS合成延迟较高，用户需要等待完整回复生成后才能听到语音。需要实现按句子分段的流式播放。

**✅ 已实现功能**：
- ✅ 按句号、问号、感叹号分割文本
- ✅ 逐句并行合成TTS音频
- ✅ 第一句合成完成后立即开始播放
- ✅ 后续句子自动排队播放
- ✅ 支持播放进度回调和完成通知
- ✅ 错误处理和重试机制
- ✅ 播放状态管理（播放/暂停/停止）

**技术实现**：
```swift
class StreamingTTSManager {
    private var playbackQueue: [TTSAudioSegment] = []
    private var isPlaying: Bool = false

    func startStreamingPlayback(_ text: String, emotion: CanCanEmotion) async {
        let sentences = splitTextIntoSentences(text)
        await processSentencesStreaming(sentences, emotion: emotion)
    }
}
```

**已创建文件**：
- ✅ `travel with/StreamingTTSManager.swift` - 流式TTS管理器
- ✅ `travel with/TTSService.swift` - 添加了synthesizeTextToData方法
- ✅ `travel with/MultiAgentIntegrationService.swift` - 集成流式播放

**完成时间**：2025年8月2日

---

### 3. 修复行动规划智能体错误触发 (🔥)
**问题描述**：即使JSON决策中设置`action_planning_agent: false`，行动规划智能体仍然被触发执行。

**排查方向**：
- 检查是否有其他地方自动调用AIActionPlanningAgent
- 确认JSON解析和条件判断逻辑
- 检查BaseAgent或其他基类是否有自动触发逻辑

**文件位置**：
- `travel with/JSONTaskScheduler.swift` - 检查条件判断
- `travel with/AIActionPlanningAgent.swift` - 检查是否有自动执行逻辑
- `travel with/BaseAgent.swift` - 检查基类逻辑

---

## 🔧 中优先级 TODO

### 4. 优化任务分配智能体的chat_text生成
**问题描述**：当前chat_text会被修改为"用户说..."的格式，应该直接传递用户原始输入。

**状态**：✅ 已修复

---

### 5. 增强长期记忆上下文集成
**问题描述**：需要在chat_text中包含长期记忆检索结果的总结。

**状态**：✅ 已实现

---

### 6. 添加系统时间传递
**问题描述**：需要从系统获取当前时间并传递给AI回复模块。

**状态**：✅ 已实现

---

## 📝 低优先级 TODO

### 7. 完善错误处理机制
- 网络请求失败的优雅降级
- API端点不可用时的备用方案
- JSON解析失败的恢复机制

### 8. 性能监控和优化
- 添加各智能体处理时间监控
- 实现智能体负载均衡
- 优化并行处理效率

### 9. 用户体验优化
- 添加处理进度指示器
- 实现对话历史搜索功能
- 支持对话导出和备份

---

## 📊 开发进度跟踪

| 任务 | 优先级 | 状态 | 负责人 | 预计完成时间 |
|------|--------|------|--------|-------------|
| 长期记忆RAG优化 | 🔥🔥🔥 | 待开始 | - | - |
| TTS流式播放 | 🔥🔥 | ✅ 已完成 | - | 2025/8/2 |
| 修复行动规划触发 | 🔥 | ✅ 已完成 | - | 2025/8/2 |
| chat_text优化 | 🔧 | ✅ 已完成 | - | 2025/8/2 |
| 长期记忆集成 | 🔧 | ✅ 已完成 | - | 2025/8/2 |
| 系统时间传递 | 🔧 | ✅ 已完成 | - | 2025/8/2 |

---

## 🔍 技术债务

### 1. 代码重构需求
- MultiAgentIntegrationService类过于庞大，需要拆分
- 智能体之间的依赖关系需要更清晰的定义
- 错误处理逻辑需要统一化

### 2. 测试覆盖率
- 缺少单元测试覆盖
- 需要集成测试验证多智能体协作
- 性能测试和压力测试

### 3. 文档完善
- API文档需要更新
- 架构图需要可视化
- 开发指南需要详细化

---

## 📞 联系和协作

**更新频率**：每次重要功能开发完成后更新
**责任人**：开发团队
**审查周期**：每周回顾一次优先级和进度

---

**最后更新**：2025年8月2日
**版本**：v1.0
