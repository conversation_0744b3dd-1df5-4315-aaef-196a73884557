//
//  ChatHistoryView.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

struct ChatHistoryView: View {
    @EnvironmentObject var aiService: AIService
    @State private var sessionStats: (messageCount: Int, lastMessageTime: Date?) = (0, nil)
    
    var body: some View {
            VStack(spacing: 0) {
                // 统计信息卡片
                VStack(spacing: 12) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("对话统计")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text("总消息数: \(sessionStats.messageCount)")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            if let lastTime = sessionStats.lastMessageTime {
                                Text("最后聊天: \(formatDate(lastTime))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .padding(.horizontal)
                .padding(.top)
                
                // 历史消息列表
                if let historyManager = aiService.historyManager {
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            // 加载更多按钮
                            if historyManager.hasMoreHistory {
                                Button(action: {
                                    Task {
                                        await loadMoreHistory()
                                    }
                                }) {
                                    HStack {
                                        if historyManager.isLoading {
                                            ProgressView()
                                                .scaleEffect(0.8)
                                        }
                                        Text(historyManager.isLoading ? "加载中..." : "加载更多历史")
                                            .font(.caption)
                                            .foregroundColor(.blue)
                                    }
                                    .padding(.vertical, 8)
                                    .padding(.horizontal, 16)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(20)
                                }
                                .padding(.horizontal)
                                .padding(.bottom, 8)
                            }
                            
                            ForEach(historyManager.recentMessages, id: \.id) { history in
                                HistoryMessageView(history: history)
                            }
                        }
                        .padding(.horizontal)
                        .padding(.top)
                    }
                } else {
                    Spacer()
                    Text("历史记录不可用")
                        .foregroundColor(.secondary)
                    Spacer()
                }
            }
            .navigationTitle("对话历史")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                loadSessionStats()
            }
    }
    
    // MARK: - 辅助方法
    
    private func loadSessionStats() {
        Task {
            if let historyManager = aiService.historyManager {
                sessionStats = await historyManager.getSessionStats()
            }
        }
    }
    
    private func loadMoreHistory() async {
        await aiService.historyManager?.loadMoreHistory()
    }
    

    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - 历史消息视图
struct HistoryMessageView: View {
    let history: ChatHistory
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 头像
            Circle()
                .fill(history.isFromUser ? Color.blue : Color.green)
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: history.isFromUser ? "person.fill" : "brain.head.profile")
                        .foregroundColor(.white)
                        .font(.caption)
                )
            
            // 消息内容
            VStack(alignment: .leading, spacing: 4) {
                // 发送者和时间
                HStack {
                    Text(history.isFromUser ? "我" : "AI朋友")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(history.isFromUser ? .blue : .green)
                    
                    Spacer()
                    
                    Text(formatTime(history.timestamp))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                // 消息内容
                Text(history.imageDescription ?? history.content)
                    .font(.body)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.leading)
                
                // 消息类型标识
                if history.messageType != "text" {
                    Text(history.messageType.uppercased())
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.secondary.opacity(0.2))
                        .cornerRadius(4)
                }
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - 预览
#Preview {
    ChatHistoryView()
        .environmentObject(AIService.preview)
}
