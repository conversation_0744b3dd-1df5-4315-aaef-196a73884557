//
//  ProfileView.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

struct ProfileView: View {
    @EnvironmentObject var aiService: AIService
    @State private var showingAIConfig = false
    @State private var showingTravelHistory = false
    @State private var showingSettings = false

    @State private var userName = "旅行者"
    @State private var userAvatar = "person.crop.circle.fill"
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 用户信息头部
                    ProfileHeaderView(
                        userName: $userName,
                        userAvatar: $userAvatar,
                        aiConnectionStatus: aiService.connectionStatus
                    )
                    
                    // AI配置卡片
                    SettingsCardView(title: "AI朋友设置", icon: "heart.circle.fill", color: .orange) {
                        VStack(spacing: 12) {
                            SettingsRowView(
                                title: "AI角色设定",
                                subtitle: "自定义AI朋友的性格和风格",
                                icon: "person.2.circle",
                                action: { showingAIConfig = true }
                            )

                            NavigationLink(destination: ChatHistoryView().environmentObject(aiService)) {
                                SettingsRowView(
                                    title: "对话历史",
                                    subtitle: "查看与AI朋友的聊天记录",
                                    icon: "message.circle",
                                    action: { }
                                )
                            }
                            .buttonStyle(PlainButtonStyle())

                            SettingsRowView(
                                title: "重新连接",
                                subtitle: "重新连接AI服务",
                                icon: "arrow.clockwise.circle",
                                action: {
                                    Task {
                                        await aiService.reconnect()
                                    }
                                }
                            )
                        }
                    }
                    
                    // 旅行管理卡片
                    SettingsCardView(title: "旅行管理", icon: "map.circle.fill", color: .blue) {
                        VStack(spacing: 12) {
                            SettingsRowView(
                                title: "旅行历史",
                                subtitle: "查看过往的旅行记录和路线",
                                icon: "clock.arrow.circlepath",
                                action: { showingTravelHistory = true }
                            )

                            SettingsRowView(
                                title: "收藏地点",
                                subtitle: "管理收藏的景点和地址",
                                icon: "heart.circle",
                                action: { /* 查看收藏 */ }
                            )

                            SettingsRowView(
                                title: "攻略收藏",
                                subtitle: "查看保存的旅行攻略",
                                icon: "book.circle",
                                action: { /* 查看攻略 */ }
                            )
                        }
                    }
                    
                    // 应用设置卡片
                    SettingsCardView(title: "应用设置", icon: "gearshape.circle.fill", color: .purple) {
                        VStack(spacing: 12) {
                            SettingsRowView(
                                title: "通知设置",
                                subtitle: "管理推送通知和提醒",
                                icon: "bell.circle",
                                action: { /* 通知设置 */ }
                            )
                            
                            SettingsRowView(
                                title: "隐私设置",
                                subtitle: "位置权限和数据隐私",
                                icon: "lock.circle",
                                action: { /* 隐私设置 */ }
                            )
                            

                        }
                    }
                    
                    // 关于应用卡片
                    SettingsCardView(title: "关于", icon: "info.circle.fill", color: .green) {
                        VStack(spacing: 12) {
                            SettingsRowView(
                                title: "应用版本",
                                subtitle: "Travel With v1.0.0",
                                icon: "app.badge",
                                action: { /* 版本信息 */ }
                            )
                            
                            SettingsRowView(
                                title: "用户反馈",
                                subtitle: "告诉我们您的想法",
                                icon: "envelope.circle",
                                action: { /* 反馈 */ }
                            )
                            
                            SettingsRowView(
                                title: "分享应用",
                                subtitle: "推荐给朋友",
                                icon: "square.and.arrow.up.circle",
                                action: { /* 分享 */ }
                            )
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("我的")
            .navigationBarTitleDisplayMode(.large)
            .background(
                LinearGradient(
                    colors: [Color(.systemBackground), Color.blue.opacity(0.05)],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            )
        }
        .sheet(isPresented: $showingAIConfig) {
            AIConfigurationView()
        }
        .sheet(isPresented: $showingTravelHistory) {
            TravelHistoryView()
        }

    }
    

}

// MARK: - 用户信息头部
struct ProfileHeaderView: View {
    @Binding var userName: String
    @Binding var userAvatar: String
    let aiConnectionStatus: AIService.ConnectionStatus
    
    var body: some View {
        VStack(spacing: 16) {
            // 用户头像
            Image(systemName: userAvatar)
                .font(.system(size: 60))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 80, height: 80)
                .background(
                    Circle()
                        .fill(Color(.systemGray6))
                )
            
            // 用户名
            Text(userName)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            // AI连接状态
            HStack(spacing: 8) {
                Circle()
                    .fill(connectionStatusColor)
                    .frame(width: 8, height: 8)
                Text("AI朋友 \(connectionStatusText)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private var connectionStatusColor: Color {
        switch aiConnectionStatus {
        case .connected: return .green
        case .connecting: return .orange
        case .disconnected, .error: return .red
        }
    }
    
    private var connectionStatusText: String {
        switch aiConnectionStatus {
        case .connected: return "在线"
        case .connecting: return "连接中"
        case .disconnected: return "离线"
        case .error: return "错误"
        }
    }
}

// MARK: - 设置卡片
struct SettingsCardView<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    @ViewBuilder let content: Content
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 卡片标题
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title3)
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                Spacer()
            }
            
            // 卡片内容
            content
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - 设置行
struct SettingsRowView: View {
    let title: String
    let subtitle: String
    let icon: String
    var isDestructive: Bool = false
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .foregroundColor(isDestructive ? .red : .blue)
                    .font(.title3)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(isDestructive ? .red : .primary)
                        .multilineTextAlignment(.leading)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        }
        .buttonStyle(.plain)
    }
}

// MARK: - AI角色设定页面
struct AIConfigurationView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var aiService: AIService
    @State private var systemPrompt: String = ""
    @State private var isEditing = false
    @State private var showingSaveAlert = false
    @State private var hasChanges = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 说明卡片
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "info.circle.fill")
                                .foregroundColor(.blue)
                            Text("AI角色设定")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }

                        Text("自定义AI朋友的性格和对话风格。这里的设定将影响AI在所有对话中的表现，包括聊天、图像识别回复等。")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(nil)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)

                    // 系统提示词编辑区域
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("系统提示词")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Spacer()

                            Button(isEditing ? "完成" : "编辑") {
                                if isEditing {
                                    // 完成编辑
                                    isEditing = false
                                    if hasChanges {
                                        showingSaveAlert = true
                                    }
                                } else {
                                    // 开始编辑
                                    isEditing = true
                                }
                            }
                            .font(.subheadline)
                            .foregroundColor(.blue)
                        }

                        if isEditing {
                            TextEditor(text: $systemPrompt)
                                .frame(minHeight: 150)
                                .padding(8)
                                .background(Color(.systemBackground))
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color(.systemGray4), lineWidth: 1)
                                )
                                .onChange(of: systemPrompt) { _, newValue in
                                    hasChanges = newValue != aiService.getCurrentSystemPrompt()
                                }
                        } else {
                            Text(systemPrompt)
                                .font(.body)
                                .foregroundColor(.primary)
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                        }
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)

                    // 重置按钮
                    Button(action: {
                        systemPrompt = AIRoleSettings.defaultPrompt
                        hasChanges = true
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("恢复默认设定")
                        }
                        .font(.subheadline)
                        .foregroundColor(.orange)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.orange.opacity(0.1))
                        .cornerRadius(12)
                    }

                    Spacer()
                }
                .padding()
            }
            .navigationTitle("AI角色设定")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        if hasChanges {
                            // 如果有未保存的更改，恢复原始值
                            systemPrompt = aiService.getCurrentSystemPrompt()
                        }
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveSettings()
                    }
                    .disabled(!hasChanges)
                }
            }
            .onAppear {
                systemPrompt = aiService.getCurrentSystemPrompt()
            }
            .alert("保存设定", isPresented: $showingSaveAlert) {
                Button("保存") {
                    saveSettings()
                }
                Button("取消", role: .cancel) {
                    systemPrompt = aiService.getCurrentSystemPrompt()
                    hasChanges = false
                }
            } message: {
                Text("是否保存对AI角色设定的更改？")
            }
        }
    }

    private func saveSettings() {
        aiService.saveRoleSettings(systemPrompt)
        hasChanges = false
        dismiss()
    }
}

struct TravelHistoryView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("旅行历史页面")
                    .font(.title)
                Text("即将开发...")
                    .foregroundColor(.secondary)
            }
            .navigationTitle("旅行历史")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 预览
#Preview {
    ProfileView()
        .environmentObject(AIService.preview)
}
