//
//  BaseAgent.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

// MARK: - 基础智能体类
/// 提供智能体的基础实现，其他具体智能体可以继承此类
@MainActor
class BaseAgent: Agent {
    
    // MARK: - Agent协议实现
    
    let identifier: AgentIdentifier
    let name: String
    let description: String
    let sharedState: SharedStateHub
    
    @Published var isReady: Bool = false
    
    // MARK: - 内部属性
    
    /// 调度器引用（用于API调用）
    weak var scheduler: AgentScheduler?
    
    /// 处理统计
    private var processCount: Int = 0
    private var totalProcessingTime: TimeInterval = 0
    private var lastProcessTime: Date?
    
    // MARK: - 初始化
    
    init(identifier: AgentIdentifier, 
         name: String, 
         description: String, 
         sharedState: SharedStateHub, 
         scheduler: AgentScheduler?) {
        self.identifier = identifier
        self.name = name
        self.description = description
        self.sharedState = sharedState
        self.scheduler = scheduler
        
        print("🤖 创建智能体: \(name)")
    }
    
    // MARK: - Agent协议方法
    
    /// 初始化智能体（子类可重写）
    func initialize() async {
        print("🔄 初始化智能体: \(name)")
        
        // 等待共享状态就绪
        while !sharedState.isSystemReady() {
            try? await Task.sleep(nanoseconds: 100_000_000)
        }
        
        // 执行具体初始化逻辑
        await performSpecificInitialization()
        
        isReady = true
        print("✅ 智能体 \(name) 初始化完成")
    }
    
    /// 处理输入（子类必须重写）
    func process(_ input: AgentInput) async -> AgentOutput {
        let startTime = Date()
        
        print("🔄 \(name) 开始处理输入...")
        
        // 更新统计信息
        processCount += 1
        lastProcessTime = startTime
        
        // 执行具体处理逻辑
        let result = await performSpecificProcessing(input)
        
        // 更新处理时间统计
        let processingTime = Date().timeIntervalSince(startTime)
        totalProcessingTime += processingTime
        
        print("✅ \(name) 处理完成，耗时: \(String(format: "%.2f", processingTime))秒")
        
        return AgentOutput(
            content: result.content,
            confidence: result.confidence,
            processingTime: processingTime,
            emotionRecommendation: result.emotionRecommendation,
            ttsEmotion: result.ttsEmotion,
            metadata: result.metadata,
            agentId: identifier
        )
    }
    
    /// 判断是否能处理特定意图（子类可重写）
    func canHandle(_ intent: UserIntent) -> Bool {
        return getHandleableIntents().contains(intent)
    }
    
    /// 获取处理优先级（子类可重写）
    func getPriority(for intent: UserIntent) -> Double {
        return canHandle(intent) ? getBasePriority() : 0.0
    }
    
    /// 获取状态摘要
    func getStatusSummary() -> String {
        let status = isReady ? "✅ 就绪" : "⏳ 初始化中"
        let avgTime = processCount > 0 ? totalProcessingTime / Double(processCount) : 0
        let lastTime = lastProcessTime?.formatted(date: .omitted, time: .shortened) ?? "从未"
        
        return """
        智能体: \(name)
        状态: \(status)
        处理次数: \(processCount)
        平均耗时: \(String(format: "%.2f", avgTime))秒
        最后处理: \(lastTime)
        """
    }
    
    // MARK: - 子类需要重写的方法
    
    /// 执行具体的初始化逻辑（子类重写）
    func performSpecificInitialization() async {
        // 默认实现为空，子类可重写
    }
    
    /// 执行具体的处理逻辑（子类必须重写）
    func performSpecificProcessing(_ input: AgentInput) async -> ProcessingResult {
        fatalError("子类必须重写 performSpecificProcessing 方法")
    }
    
    /// 获取可处理的意图列表（子类重写）
    func getHandleableIntents() -> [UserIntent] {
        return []
    }
    
    /// 获取基础优先级（子类重写）
    func getBasePriority() -> Double {
        return 0.5
    }
    
    // MARK: - 辅助方法
    
    /// 调用API的便捷方法
    func callAPI(systemPrompt: String, userMessage: String) async -> String {
        guard let scheduler = scheduler else {
            print("⚠️ 调度器不可用，无法调用API")
            return "系统暂时不可用"
        }
        
        let messages = [
            APIMessage(role: "system", content: systemPrompt),
            APIMessage(role: "user", content: userMessage)
        ]
        
        return await scheduler.callAPI(with: identifier, messages: messages)
    }
    
    /// 生成带有上下文的系统提示词
    func generateContextualSystemPrompt(basePrompt: String) -> String {
        let personality = sharedState.getCurrentPersonality()
        let lifeStatus = sharedState.getCurrentLifeStatus()
        
        return """
        \(basePrompt)
        
        === 当前AI状态 ===
        \(personality)
        
        === AI生活状态 ===
        \(lifeStatus)
        
        请基于以上信息进行回复。
        """
    }
    
    /// 分析用户情感的辅助方法
    func analyzeUserEmotion(_ message: String) -> EmotionState {
        let lowercased = message.lowercased()
        
        if lowercased.contains("开心") || lowercased.contains("高兴") || lowercased.contains("哈哈") {
            return EmotionState(primary: .happy, intensity: 0.8)
        } else if lowercased.contains("难过") || lowercased.contains("伤心") {
            return EmotionState(primary: .concerned, intensity: 0.7)
        } else if lowercased.contains("累") || lowercased.contains("疲惫") {
            return EmotionState(primary: .calm, intensity: 0.5)
        } else if lowercased.contains("?") || lowercased.contains("？") {
            return EmotionState(primary: .curious, intensity: 0.6)
        } else {
            return EmotionState(primary: .friendly, intensity: 0.5)
        }
    }
    
    /// 推荐TTS情感音色
    func recommendTTSEmotion(for aiEmotion: EmotionState) -> CanCanEmotion {
        let recommendedEmotion: CanCanEmotion

        switch aiEmotion.primary {
        case .happy: recommendedEmotion = .happy
        case .excited: recommendedEmotion = .happy
        case .curious: recommendedEmotion = .pleased
        case .calm: recommendedEmotion = .yoga
        case .concerned: recommendedEmotion = .comfort
        case .empathetic: recommendedEmotion = .comfort
        case .playful: recommendedEmotion = .loveyDovey
        case .thoughtful: recommendedEmotion = .professional
        case .caring: recommendedEmotion = .comfort
        case .helpful: recommendedEmotion = .pleased
        case .friendly: recommendedEmotion = .pleased
        }

        print("🎭 BaseAgent推荐TTS情感: \(aiEmotion.primary) -> \(recommendedEmotion.description) (\(recommendedEmotion.rawValue))")

        return recommendedEmotion
    }
}

// MARK: - 处理结果模型

/// 智能体处理结果
struct ProcessingResult {
    let content: String
    let confidence: Double
    let emotionRecommendation: EmotionState?
    let ttsEmotion: CanCanEmotion?
    let metadata: [String: Any]
    
    init(content: String, 
         confidence: Double = 1.0, 
         emotionRecommendation: EmotionState? = nil, 
         ttsEmotion: CanCanEmotion? = nil, 
         metadata: [String: Any] = [:]) {
        self.content = content
        self.confidence = confidence
        self.emotionRecommendation = emotionRecommendation
        self.ttsEmotion = ttsEmotion
        self.metadata = metadata
    }
}
