//
//  TTSTestView.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/2.
//  TTS功能测试视图
//

import SwiftUI

struct TTSTestView: View {
    @StateObject private var ttsService = TTSService.shared
    @State private var testText = "你好，我是你的AI助手。欢迎使用语音合成功能！"
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                Text("TTS功能测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                // 状态显示
                VStack(spacing: 15) {
                    HStack(spacing: 12) {
                        Circle()
                            .fill(ttsService.isPlaying ? Color.orange : Color.green)
                            .frame(width: 12, height: 12)
                            .scaleEffect(ttsService.isPlaying ? 1.5 : 1.0)
                            .animation(.easeInOut(duration: 0.6).repeatForever(), value: ttsService.isPlaying)
                        
                        Text(ttsService.isPlaying ? "正在播放" : "就绪")
                            .font(.headline)
                            .foregroundColor(ttsService.isPlaying ? .orange : .green)
                    }
                    
                    if ttsService.isPlaying {
                        // 语音波形动画
                        HStack(spacing: 4) {
                            ForEach(0..<7, id: \.self) { index in
                                RoundedRectangle(cornerRadius: 2)
                                    .fill(Color.orange.opacity(0.8))
                                    .frame(width: 4, height: CGFloat.random(in: 10...30))
                                    .animation(
                                        .easeInOut(duration: 0.5)
                                        .repeatForever()
                                        .delay(Double(index) * 0.1),
                                        value: ttsService.isPlaying
                                    )
                            }
                        }
                        .padding(.top, 8)
                    }
                    
                    // 当前播放文本
                    if !ttsService.currentText.isEmpty {
                        Text("正在播放: \(ttsService.currentText)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(3)
                            .padding(.horizontal)
                    }
                    
                    // 错误信息
                    if let errorMessage = ttsService.errorMessage {
                        Text("错误: \(errorMessage)")
                            .font(.caption)
                            .foregroundColor(.red)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(15)
                
                // 测试文本输入
                VStack(alignment: .leading, spacing: 10) {
                    Text("测试文本:")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    TextEditor(text: $testText)
                        .frame(height: 100)
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                }
                
                // 控制按钮
                VStack(spacing: 15) {
                    HStack(spacing: 20) {
                        // 播放按钮
                        Button(action: {
                            ttsService.synthesizeAndPlay(text: testText)
                        }) {
                            HStack {
                                Image(systemName: "play.fill")
                                Text("播放")
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.horizontal, 30)
                            .padding(.vertical, 12)
                            .background(Color.blue)
                            .cornerRadius(25)
                        }
                        .disabled(ttsService.isPlaying)
                        
                        // 停止按钮
                        Button(action: {
                            ttsService.stopPlaying()
                        }) {
                            HStack {
                                Image(systemName: "stop.fill")
                                Text("停止")
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.horizontal, 30)
                            .padding(.vertical, 12)
                            .background(Color.red)
                            .cornerRadius(25)
                        }
                        .disabled(!ttsService.isPlaying)
                    }
                    
                    // 暂停/继续按钮
                    if ttsService.isPlaying || ttsService.currentState == .paused {
                        Button(action: {
                            if ttsService.currentState == .paused {
                                ttsService.resumePlaying()
                            } else {
                                ttsService.pausePlaying()
                            }
                        }) {
                            HStack {
                                Image(systemName: ttsService.currentState == .paused ? "play.fill" : "pause.fill")
                                Text(ttsService.currentState == .paused ? "继续" : "暂停")
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.horizontal, 30)
                            .padding(.vertical, 12)
                            .background(Color.orange)
                            .cornerRadius(25)
                        }
                    }
                }
                
                // 预设测试文本
                VStack(alignment: .leading, spacing: 10) {
                    Text("快速测试:")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 10) {
                        ForEach(presetTexts, id: \.self) { text in
                            Button(action: {
                                testText = text
                                ttsService.synthesizeAndPlay(text: text)
                            }) {
                                Text(text.prefix(15) + (text.count > 15 ? "..." : ""))
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 8)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(8)
                            }
                            .disabled(ttsService.isPlaying)
                        }
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
        }
    }
    
    // 预设测试文本
    private let presetTexts = [
        "你好，我是你的AI助手。",
        "今天天气真不错！",
        "欢迎使用语音合成功能。",
        "这是一个测试文本，用来验证TTS功能是否正常工作。",
        "人工智能技术正在改变我们的生活。",
        "感谢您使用我们的应用程序。"
    ]
}

#Preview {
    TTSTestView()
}
