//
//  VoicePermissionView.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/31.
//

import SwiftUI

// MARK: - 语音权限申请视图
struct VoicePermissionView: View {
    @StateObject private var permissionManager = PermissionManager()
    @State private var isRequestingPermissions = false
    let onComplete: () -> Void
    
    var body: some View {
        VStack(spacing: 30) {
            // 标题
            VStack(spacing: 16) {
                Image(systemName: "mic.circle.fill")
                    .font(.system(size: 80))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                Text("语音功能权限")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("为了使用语音识别功能，需要您授权麦克风权限")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            // 权限列表
            VStack(spacing: 20) {
                VoicePermissionRowView(
                    icon: "mic.fill",
                    title: "麦克风",
                    description: "用于录制您的语音，通过火山引擎ASR转换为文字",
                    status: permissionManager.microphonePermissionStatus
                )
            }
            .padding(.horizontal)
            
            Spacer()
            
            // 申请权限按钮
            VStack(spacing: 16) {
                if permissionManager.allVoicePermissionsGranted {
                    Button(action: onComplete) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                            Text("开始使用")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(12)
                    }
                } else {
                    Button(action: {
                        isRequestingPermissions = true
                        Task {
                            await permissionManager.requestVoicePermissions()
                            isRequestingPermissions = false
                        }
                    }) {
                        HStack {
                            if isRequestingPermissions {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .tint(.white)
                            } else {
                                Image(systemName: "hand.raised.fill")
                            }
                            Text(isRequestingPermissions ? "申请中..." : "申请权限")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                    }
                    .disabled(isRequestingPermissions)
                    
                    if permissionManager.microphonePermissionStatus == .denied {
                        Button("前往设置") {
                            permissionManager.openSettings()
                        }
                        .font(.subheadline)
                        .foregroundColor(.blue)
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding()
        .alert("权限提示", isPresented: $permissionManager.showingPermissionAlert) {
            Button("前往设置") {
                permissionManager.openSettings()
            }
            Button("稍后", role: .cancel) { }
        } message: {
            Text(permissionManager.permissionAlertMessage)
        }
        .onAppear {
            permissionManager.onPermissionsCompleted = onComplete
        }
    }
}

// MARK: - 语音权限行视图
struct VoicePermissionRowView: View {
    let icon: String
    let title: String
    let description: String
    let status: PermissionStatus
    
    var body: some View {
        HStack(spacing: 16) {
            // 图标
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)
            
            // 文本信息
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 状态指示器
            HStack(spacing: 8) {
                Circle()
                    .fill(getStatusColor(status))
                    .frame(width: 8, height: 8)
                
                Text(getStatusText(status))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func getStatusColor(_ status: PermissionStatus) -> Color {
        switch status {
        case .granted:
            return .green
        case .denied, .restricted:
            return .red
        case .notDetermined:
            return .orange
        }
    }
    
    private func getStatusText(_ status: PermissionStatus) -> String {
        switch status {
        case .notDetermined:
            return "待申请"
        case .granted:
            return "已授权"
        case .denied:
            return "已拒绝"
        case .restricted:
            return "受限制"
        }
    }
}

// MARK: - 启动画面
struct LaunchScreenView: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            // 应用图标和名称
            VStack(spacing: 20) {
                Image(systemName: "heart.circle.fill")
                    .font(.system(size: 100))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.orange, .pink],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .scaleEffect(isAnimating ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isAnimating)
                
                Text("Travel With")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                
                Text("AI旅行陪伴应用")
                    .font(.headline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 加载指示器
            VStack(spacing: 16) {
                ProgressView()
                    .scaleEffect(1.2)
                    .tint(.blue)
                
                Text("正在初始化...")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - 预览
#Preview {
    VoicePermissionView {
        print("权限申请完成")
    }
}
