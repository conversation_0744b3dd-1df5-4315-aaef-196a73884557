//
//  AngleSmoothingDemo.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

struct AngleSmoothingDemo: View {
    @State private var rawAngle: Double = 0
    @State private var smoothedAngle: Double = 0
    @State private var lastAngle: Double = 0
    @State private var isAnimating = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 40) {
                Text("角度平滑处理演示")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                // 原始角度（会跳跃）
                VStack(spacing: 20) {
                    Text("原始角度（会跳跃）")
                        .font(.headline)
                        .foregroundColor(.red)
                    
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.3), lineWidth: 2)
                            .frame(width: 150, height: 150)
                        
                        Image(systemName: "arrow.up.circle.fill")
                            .font(.title)
                            .foregroundColor(.red)
                            .rotationEffect(.degrees(rawAngle))
                            .animation(.easeInOut(duration: 0.3), value: rawAngle)
                    }
                    
                    Text("\(String(format: "%.1f", rawAngle))°")
                        .font(.title2)
                        .fontWeight(.semibold)
                }
                
                // 平滑后角度
                VStack(spacing: 20) {
                    Text("平滑后角度（顺滑转动）")
                        .font(.headline)
                        .foregroundColor(.green)
                    
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.3), lineWidth: 2)
                            .frame(width: 150, height: 150)
                        
                        Image(systemName: "arrow.up.circle.fill")
                            .font(.title)
                            .foregroundColor(.green)
                            .rotationEffect(.degrees(smoothedAngle))
                            .animation(.easeInOut(duration: 0.5), value: smoothedAngle)
                    }
                    
                    Text("\(String(format: "%.1f", smoothedAngle))°")
                        .font(.title2)
                        .fontWeight(.semibold)
                }
                
                // 控制按钮
                VStack(spacing: 15) {
                    Button("模拟 359° → 4° 跳跃") {
                        simulateAngleJump()
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Button("模拟连续旋转") {
                        simulateContinuousRotation()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("重置") {
                        resetAngles()
                    }
                    .buttonStyle(.bordered)
                }
                
                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
        }
    }
    
    private func simulateAngleJump() {
        // 模拟从359度跳到4度的情况
        rawAngle = 359
        smoothedAngle = smoothAngle(359)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            rawAngle = 4
            smoothedAngle = smoothAngle(4)
        }
    }
    
    private func simulateContinuousRotation() {
        guard !isAnimating else { return }
        isAnimating = true
        
        let angles: [Double] = [0, 45, 90, 135, 180, 225, 270, 315, 360, 45, 90]
        
        for (index, angle) in angles.enumerated() {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(index) * 0.5) {
                rawAngle = angle
                smoothedAngle = smoothAngle(angle)
                
                if index == angles.count - 1 {
                    isAnimating = false
                }
            }
        }
    }
    
    private func resetAngles() {
        rawAngle = 0
        smoothedAngle = 0
        lastAngle = 0
        isAnimating = false
    }
    
    // MARK: - 角度平滑处理（与MapNavigationView中相同的算法）
    private func smoothAngle(_ newAngle: Double) -> Double {
        let diff = angleDifference(from: lastAngle, to: newAngle)
        
        print("🧭 角度变化: \(String(format: "%.1f", lastAngle))° → \(String(format: "%.1f", newAngle))° (差异: \(String(format: "%.1f", diff))°)")
        
        let result: Double
        // 如果角度差异超过180度，说明发生了跳跃
        if abs(diff) > 180 {
            // 计算最短路径的角度
            let shortestDiff = diff > 0 ? diff - 360 : diff + 360
            result = lastAngle + shortestDiff
            print("🔄 检测到角度跳跃，使用最短路径: \(String(format: "%.1f", shortestDiff))°")
        } else {
            result = newAngle
            print("✅ 正常角度变化")
        }
        
        lastAngle = newAngle
        print("📍 平滑结果: \(String(format: "%.1f", result))°")
        return result
    }
    
    // 计算两个角度之间的差异
    private func angleDifference(from angle1: Double, to angle2: Double) -> Double {
        var diff = angle2 - angle1
        while diff > 180 { diff -= 360 }
        while diff < -180 { diff += 360 }
        return diff
    }
}

#Preview {
    AngleSmoothingDemo()
}
