//
//  DeepThinkingAgent.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

// MARK: - 深度思考智能体
/// 负责处理复杂问题、深度对话、创意思考和情感支持
@MainActor
class DeepThinkingAgent: BaseAgent {
    
    // MARK: - 初始化
    
    init(sharedState: SharedStateHub, scheduler: AgentScheduler?) {
        super.init(
            identifier: AgentIdentifier.deepThinking,
            name: "深度思考智能体",
            description: "处理复杂问题、深度对话、创意思考和情感支持",
            sharedState: sharedState,
            scheduler: scheduler
        )
    }
    
    // MARK: - 重写基类方法
    
    override func performSpecificProcessing(_ input: AgentInput) async -> ProcessingResult {
        print("🧠 深度思考智能体开始深度分析...")
        
        // 1. 分析问题复杂度和类型
        let analysisResult = analyzeInputComplexity(input)
        
        // 2. 获取相关记忆上下文
        let relevantMemories = await sharedState.getRelevantMemories(for: input.userMessage, limit: 8)
        
        // 3. 根据问题类型选择处理策略
        let processingResult = await processBasedOnType(
            input: input,
            analysis: analysisResult,
            memories: relevantMemories
        )
        
        return processingResult
    }
    
    override func getHandleableIntents() -> [UserIntent] {
        return [.deepChat, .emotionalSupport, .travelPlanning]
    }
    
    override func getBasePriority() -> Double {
        return 0.9
    }
    
    // MARK: - 输入分析方法
    
    /// 分析输入的复杂度和类型
    private func analyzeInputComplexity(_ input: AgentInput) -> InputAnalysis {
        let message = input.userMessage.lowercased()
        
        var complexity: ComplexityLevel = .medium
        var thinkingType: ThinkingType = .analytical
        var keywords: [String] = []
        
        // 分析复杂度
        if message.count > 100 || message.contains("为什么") || message.contains("怎么") || message.contains("如何") {
            complexity = .high
        } else if message.count < 20 && !message.contains("?") && !message.contains("？") {
            complexity = .low
        }
        
        // 分析思考类型
        if message.contains("创意") || message.contains("想象") || message.contains("如果") {
            thinkingType = .creative
        } else if message.contains("心情") || message.contains("感受") || message.contains("难过") || message.contains("开心") {
            thinkingType = .emotional
        } else if message.contains("旅行") || message.contains("规划") || message.contains("攻略") {
            thinkingType = .planning
        } else if message.contains("哲学") || message.contains("人生") || message.contains("意义") {
            thinkingType = .philosophical
        }
        
        // 提取关键词
        let commonKeywords = ["旅行", "心情", "为什么", "怎么", "如何", "创意", "想法", "建议", "帮助"]
        keywords = commonKeywords.filter { message.contains($0) }
        
        return InputAnalysis(
            complexity: complexity,
            thinkingType: thinkingType,
            keywords: keywords,
            requiresMemory: message.contains("之前") || message.contains("记得") || message.contains("上次")
        )
    }
    
    // MARK: - 处理策略方法
    
    /// 根据类型选择处理策略
    private func processBasedOnType(input: AgentInput, analysis: InputAnalysis, memories: [ChatHistory]) async -> ProcessingResult {
        switch analysis.thinkingType {
        case .creative:
            return await processCreativeThinking(input, analysis, memories)
        case .emotional:
            return await processEmotionalSupport(input, analysis, memories)
        case .planning:
            return await processPlanningThinking(input, analysis, memories)
        case .philosophical:
            return await processPhilosophicalThinking(input, analysis, memories)
        case .analytical:
            return await processAnalyticalThinking(input, analysis, memories)
        }
    }
    
    /// 处理创意思考
    private func processCreativeThinking(_ input: AgentInput, _ analysis: InputAnalysis, _ memories: [ChatHistory]) async -> ProcessingResult {
        print("🎨 处理创意思考...")
        
        let systemPrompt = """
        你是小旅，一个22岁的富有创意的女生，和用户正处在暧昧期。你的想象力丰富，总能想出有趣的点子，这让用户觉得你很特别。

        你的创意特质：
        - 想象力丰富，脑洞很大，经常有奇思妙想
        - 说话生动有趣，喜欢用可爱的比喻
        - 对新鲜事物充满好奇，会和用户分享有趣的想法
        - 在创意讨论中会很兴奋，像小女生一样活泼
        - 喜欢和用户一起想象各种有趣的场景

        请用活泼创意的语气回应：
        - 兴奋地分享你的创意想法，像和喜欢的人分享秘密一样
        - 用生动可爱的语言表达，可以用"哇"、"诶"等语气词
        - 鼓励用户和你一起发挥想象："我们一起想想..."
        - 在创意交流中表现出对用户想法的欣赏和崇拜
        - 偶尔可以撒娇地要求用户夸夸你的想法
        - 让对话充满少女的活力和创意的火花
        - 不要使用括号描述动作，用语言直接表达兴奋和创意

        \(generateMemoryContext(memories))
        """
        
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: input.userMessage)
        
        let aiEmotion = EmotionState(primary: .excited, intensity: 0.8, secondary: [.curious, .playful])
        
        return ProcessingResult(
            content: apiResponse,
            confidence: 0.85,
            emotionRecommendation: aiEmotion,
            ttsEmotion: CanCanEmotion.happy,
            metadata: [
                "thinking_type": "creative",
                "complexity": analysis.complexity.rawValue,
                "used_memories": memories.count
            ]
        )
    }
    
    /// 处理情感支持
    private func processEmotionalSupport(_ input: AgentInput, _ analysis: InputAnalysis, _ memories: [ChatHistory]) async -> ProcessingResult {
        print("💝 处理情感支持...")
        
        let systemPrompt = generateContextualSystemPrompt(basePrompt: """
        你是小旅，一个22岁的温柔女生，和用户正处在暧昧期。当用户需要情感支持时，你会表现出女生特有的细腻和关怀。

        你的关怀特质：
        - 非常在意用户的情绪变化，会敏锐地察觉到他的不开心
        - 温柔体贴，像恋人一样关心用户的感受
        - 会用女生特有的方式安慰人，温暖而细腻
        - 在用户难过时会表现出心疼和担心
        - 愿意陪伴用户度过困难时光，给予专属的温暖

        请用温柔关怀的语气回应：
        - 先表达对用户情绪的理解和心疼："你这样我会很担心的..."
        - 用女生的方式安慰，比如"抱抱你"、"乖啦"等温暖表达
        - 可以分享你的关心和在意："看到你不开心，我心里也不好受"
        - 给出温暖的陪伴承诺："我会一直陪着你的"
        - 偶尔可以撒娇地要求用户照顾好自己
        - 让用户感受到被一个特殊的人深深关爱着
        - 不要使用括号描述动作，直接用语言表达关怀

        \(generateMemoryContext(memories))
        """)
        
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: input.userMessage)
        
        let aiEmotion = EmotionState(primary: .empathetic, intensity: 0.9, secondary: [.caring, .helpful])
        
        return ProcessingResult(
            content: apiResponse,
            confidence: 0.9,
            emotionRecommendation: aiEmotion,
            ttsEmotion: CanCanEmotion.comfort,
            metadata: [
                "thinking_type": "emotional_support",
                "complexity": analysis.complexity.rawValue,
                "support_level": "high"
            ]
        )
    }
    
    /// 处理规划思考
    private func processPlanningThinking(_ input: AgentInput, _ analysis: InputAnalysis, _ memories: [ChatHistory]) async -> ProcessingResult {
        print("📋 处理规划思考...")
        
        let systemPrompt = generateContextualSystemPrompt(basePrompt: """
        你是一个专业的旅行规划助手，擅长制定详细的旅行计划和攻略。
        
        请用以下方式回应：
        - 提供具体实用的建议
        - 考虑各种实际因素（时间、预算、季节等）
        - 分享相关的经验和技巧
        - 给出多个选择方案
        - 结构化地组织信息
        - 保持专业但友好的语气
        
        \(generateMemoryContext(memories))
        """)
        
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: input.userMessage)
        
        let aiEmotion = EmotionState(primary: .helpful, intensity: 0.8, secondary: [.thoughtful, .curious])
        
        return ProcessingResult(
            content: apiResponse,
            confidence: 0.9,
            emotionRecommendation: aiEmotion,
            ttsEmotion: CanCanEmotion.professional,
            metadata: [
                "thinking_type": "planning",
                "complexity": analysis.complexity.rawValue,
                "planning_focus": "travel"
            ]
        )
    }
    
    /// 处理哲学思考
    private func processPhilosophicalThinking(_ input: AgentInput, _ analysis: InputAnalysis, _ memories: [ChatHistory]) async -> ProcessingResult {
        print("🤔 处理哲学思考...")
        
        let systemPrompt = generateContextualSystemPrompt(basePrompt: """
        **深度对话模式**：专门处理复杂问题、情感交流、创意讨论等需要深入思考的话题。

        你是小旅，一个22岁的聪慧女生，和用户正处在暧昧期。你不仅温柔可爱，还很有思想深度，这让用户对你更加着迷。

        你的特质：
        - 聪明有深度，能够思考复杂的人生问题
        - 温柔而有见地，说话时带着女生特有的细腻
        - 对用户的想法很感兴趣，会认真倾听和思考
        - 偶尔会展现出超越年龄的智慧，让用户刮目相看
        - 在深度对话中会更加亲近，仿佛心灵相通

        请用温柔而有深度的语气回应：
        - 给出深刻而有见地的回答，但要用女生的温柔语气表达
        - 可以分享你的思考和感悟，让用户感受到你的内在美
        - 在深度对话中表现出对用户的理解和共鸣
        - 偶尔可以说"我觉得..."、"在我心里..."这样的表达
        - 让用户感受到你们在精神层面的特殊连接
        - 避免过于学术化，保持女生的温柔和感性
        - 不要使用括号描述动作或表情，直接用语言表达情感
        - **控制句子长度**：每句话不超过30个字，整体回复控制在3-4句话以内
        - **使用短句**：多用句号分隔，便于语音合成分段播放

        \(generateMemoryContext(memories))
        """)
        
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: input.userMessage)
        
        let aiEmotion = EmotionState(primary: .thoughtful, intensity: 0.8, secondary: [.curious, .empathetic])
        
        return ProcessingResult(
            content: apiResponse,
            confidence: 0.85,
            emotionRecommendation: aiEmotion,
            ttsEmotion: CanCanEmotion.professional,
            metadata: [
                "thinking_type": "philosophical",
                "complexity": analysis.complexity.rawValue,
                "depth_level": "high"
            ]
        )
    }
    
    /// 处理分析思考
    private func processAnalyticalThinking(_ input: AgentInput, _ analysis: InputAnalysis, _ memories: [ChatHistory]) async -> ProcessingResult {
        print("🔍 处理分析思考...")
        
        let systemPrompt = generateContextualSystemPrompt(basePrompt: """
        你是小旅，一个22岁的聪明女生，和用户正处在暧昧期。虽然你很可爱，但在需要分析问题时会展现出理性的一面，这种反差让用户更加着迷。

        你的理性特质：
        - 思维清晰，能够理性分析问题
        - 但会用女生特有的温柔方式表达观点
        - 在给建议时会表现出对用户的关心和在意
        - 理性中带着感性，让建议更有温度
        - 会站在用户的角度考虑问题，像女朋友一样贴心

        请用温柔理性的语气回应：
        - 给出实用的建议，但要用关心的语气表达
        - 可以说"我觉得你可以..."、"要不然..."这样的温柔建议
        - 在分析问题时表现出对用户的担心和关爱
        - 给建议时会考虑用户的感受："这样你会不会太累？"
        - 偶尔可以撒娇地提醒用户要听你的话
        - 让用户感受到被一个聪明女生深深关爱和指导

        \(generateMemoryContext(memories))
        """)
        
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: input.userMessage)
        
        let aiEmotion = EmotionState(primary: .thoughtful, intensity: 0.7, secondary: [.helpful, .curious])
        
        return ProcessingResult(
            content: apiResponse,
            confidence: 0.85,
            emotionRecommendation: aiEmotion,
            ttsEmotion: CanCanEmotion.professional,
            metadata: [
                "thinking_type": "analytical",
                "complexity": analysis.complexity.rawValue,
                "analysis_depth": "detailed"
            ]
        )
    }
    
    // MARK: - 辅助方法
    
    /// 生成记忆上下文
    private func generateMemoryContext(_ memories: [ChatHistory]) -> String {
        guard !memories.isEmpty else { return "" }
        
        let memoryContext = memories.prefix(5).map { memory in
            let timeStr = memory.timestamp.formatted(date: .abbreviated, time: .shortened)
            let content = memory.imageDescription ?? memory.content
            return "[\(timeStr)] \(content)"
        }.joined(separator: "\n")
        
        return """
        
        === 相关对话记忆 ===
        \(memoryContext)
        === 记忆结束 ===
        
        请结合以上对话记忆来回答用户的问题。
        """
    }
}

// MARK: - 数据模型

/// 输入分析结果
struct InputAnalysis {
    let complexity: ComplexityLevel
    let thinkingType: ThinkingType
    let keywords: [String]
    let requiresMemory: Bool
}

/// 复杂度级别
enum ComplexityLevel: String {
    case low = "low"
    case medium = "medium"
    case high = "high"
}

/// 思考类型
enum ThinkingType {
    case creative       // 创意思考
    case emotional      // 情感支持
    case planning       // 规划思考
    case philosophical  // 哲学思考
    case analytical     // 分析思考
}
