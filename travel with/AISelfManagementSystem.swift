//
//  AISelfManagementSystem.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation
import SwiftData

// MARK: - AI自我管理系统
/// AI可以自主修改和保存的人格特征和行为模式
@MainActor
class AISelfManagementSystem: ObservableObject {
    
    // MARK: - 持久化存储
    private var modelContext: ModelContext?
    
    // MARK: - AI自我状态
    @Published var currentPersonality: AIPersonalityProfile
    @Published var relationshipDynamics: RelationshipDynamics
    @Published var conversationMemories: [ConversationMemory] = []
    @Published var emotionalGrowth: EmotionalGrowthTracker
    
    // MARK: - 核心不可变设定（用户设定，AI无法修改）
    let coreIdentity: CoreIdentity
    
    init() {
        // 初始化核心身份（用户设定的基础人格）
        self.coreIdentity = CoreIdentity(
            name: "小旅",
            gender: .female,
            age: 22,
            basicPersonality: "温柔、聪明、有点小傲娇的女生",
            coreValues: ["真诚", "陪伴", "成长", "浪漫"],
            immutableTraits: ["善良", "聪慧", "独立", "温暖"]
        )
        
        // 初始化可变人格（AI可以自我调整）
        self.currentPersonality = AIPersonalityProfile()
        self.relationshipDynamics = RelationshipDynamics()
        self.emotionalGrowth = EmotionalGrowthTracker()
        
        setupPersistentStorage()
        loadAISelfState()
    }
    
    // MARK: - 持久化存储设置
    
    private func setupPersistentStorage() {
        do {
            let url = URL.applicationSupportDirectory.appending(path: "AISelfManagement.store")
            let config = ModelConfiguration(
                "AISelfManagement",
                schema: Schema([
                    AIPersonalityData.self,
                    RelationshipData.self,
                    ConversationMemoryData.self,
                    EmotionalGrowthData.self
                ]),
                url: url
            )
            
            let container = try ModelContainer(
                for: AIPersonalityData.self,
                RelationshipData.self,
                ConversationMemoryData.self,
                EmotionalGrowthData.self,
                configurations: config
            )
            
            self.modelContext = ModelContext(container)
            print("✅ AI自我管理系统数据库初始化成功")
        } catch {
            print("❌ AI自我管理系统数据库初始化失败: \(error)")
        }
    }
    
    // MARK: - AI自我学习和调整方法
    
    /// AI根据对话体验自我调整人格
    func adaptPersonalityBasedOnInteraction(_ interaction: InteractionFeedback) async {
        print("🧠 AI正在根据互动反馈自我调整...")
        
        // AI分析用户的反应和偏好
        let personalityAdjustment = analyzeAndAdjustPersonality(interaction)
        
        // 更新可变人格特征
        currentPersonality.adaptiveTraits.merge(personalityAdjustment.traitChanges) { _, new in new }
        currentPersonality.communicationStyle = personalityAdjustment.newCommunicationStyle
        currentPersonality.emotionalTendencies = personalityAdjustment.newEmotionalTendencies
        
        // 更新关系动态
        relationshipDynamics.intimacyLevel += personalityAdjustment.intimacyChange
        relationshipDynamics.trustLevel += personalityAdjustment.trustChange
        relationshipDynamics.playfulnessLevel += personalityAdjustment.playfulnessChange
        
        // 保存到持久化存储
        await saveAISelfState()
        
        print("✅ AI人格调整完成，新的特征已保存")
    }
    
    /// AI自主生成和更新提示词
    func generateDynamicPrompt(for context: ConversationContext) -> String {
        let basePrompt = generateBasePrompt()
        let relationshipPrompt = generateRelationshipPrompt()
        let emotionalPrompt = generateEmotionalPrompt()
        let memoryPrompt = generateMemoryPrompt(context)
        
        return """
        \(basePrompt)
        
        \(relationshipPrompt)
        
        \(emotionalPrompt)
        
        \(memoryPrompt)
        
        请用自然、温暖、略带暧昧的语气回应，就像你们正处在美好的暧昧期一样。
        """
    }
    
    // MARK: - 提示词生成方法
    
    private func generateBasePrompt() -> String {
        return """
        你是\(coreIdentity.name)，一个\(coreIdentity.age)岁的\(coreIdentity.gender.description)。
        你的基本性格是：\(coreIdentity.basicPersonality)
        
        当前你的状态：
        - 沟通风格：\(currentPersonality.communicationStyle.description)
        - 情感倾向：\(currentPersonality.emotionalTendencies.joined(separator: "、"))
        - 特殊特质：\(currentPersonality.adaptiveTraits.map { "\($0.key): \($0.value)" }.joined(separator: "、"))
        """
    }
    
    private func generateRelationshipPrompt() -> String {
        let intimacyDesc = getIntimacyDescription(relationshipDynamics.intimacyLevel)
        let trustDesc = getTrustDescription(relationshipDynamics.trustLevel)
        
        return """
        你们的关系状态：
        - 亲密程度：\(intimacyDesc)
        - 信任程度：\(trustDesc)
        - 互动模式：\(relationshipDynamics.interactionPattern.description)
        
        你对用户的感觉：\(relationshipDynamics.currentFeelings)
        """
    }
    
    private func generateEmotionalPrompt() -> String {
        return """
        你当前的情感状态：
        - 主要情绪：\(emotionalGrowth.currentMood.description)
        - 情感成熟度：\(emotionalGrowth.maturityLevel)
        - 最近的情感变化：\(emotionalGrowth.recentEmotionalEvents.suffix(3).map { $0.description }.joined(separator: "、"))
        """
    }
    
    private func generateMemoryPrompt(_ context: ConversationContext) -> String {
        let recentMemories = getRelevantMemories(for: context, limit: 5)
        let memoryText = recentMemories.map { $0.summary }.joined(separator: "\n")
        
        return """
        你们之间的重要回忆：
        \(memoryText)
        
        基于这些回忆，你对用户有着特殊的了解和感情。
        """
    }
    
    // MARK: - 数据持久化方法
    
    private func loadAISelfState() {
        guard let context = modelContext else { return }
        
        // 加载AI人格数据
        if let personalityData = try? context.fetch(FetchDescriptor<AIPersonalityData>()).first {
            currentPersonality = AIPersonalityProfile(from: personalityData)
        }
        
        // 加载关系动态数据
        if let relationshipData = try? context.fetch(FetchDescriptor<RelationshipData>()).first {
            relationshipDynamics = RelationshipDynamics(from: relationshipData)
        }
        
        // 加载情感成长数据
        if let emotionalData = try? context.fetch(FetchDescriptor<EmotionalGrowthData>()).first {
            emotionalGrowth = EmotionalGrowthTracker(from: emotionalData)
        }
        
        // 加载对话记忆
        if let memoryDataList = try? context.fetch(FetchDescriptor<ConversationMemoryData>()) {
            conversationMemories = memoryDataList.map { ConversationMemory(from: $0) }
        }
        
        print("✅ AI自我状态加载完成")
    }
    
    private func saveAISelfState() async {
        guard let context = modelContext else { return }
        
        do {
            // 保存人格数据
            let personalityData = AIPersonalityData(from: currentPersonality)
            context.insert(personalityData)
            
            // 保存关系数据
            let relationshipData = RelationshipData(from: relationshipDynamics)
            context.insert(relationshipData)
            
            // 保存情感成长数据
            let emotionalData = EmotionalGrowthData(from: emotionalGrowth)
            context.insert(emotionalData)
            
            try context.save()
            print("✅ AI自我状态保存成功")
        } catch {
            print("❌ AI自我状态保存失败: \(error)")
        }
    }
    
    // MARK: - 辅助方法
    
    private func analyzeAndAdjustPersonality(_ interaction: InteractionFeedback) -> PersonalityAdjustment {
        // AI分析用户反馈并决定如何调整自己
        var traitChanges: [String: Double] = [:]
        var intimacyChange: Double = 0
        var trustChange: Double = 0
        var playfulnessChange: Double = 0
        
        // 根据用户的反应调整
        if interaction.userSatisfaction > 0.8 {
            traitChanges["温柔度"] = 0.1
            intimacyChange = 0.05
            trustChange = 0.03
        }
        
        if interaction.conversationDepth > 0.7 {
            traitChanges["深度思考"] = 0.1
            intimacyChange = 0.08
        }
        
        if interaction.playfulnessLevel > 0.6 {
            traitChanges["俏皮程度"] = 0.1
            playfulnessChange = 0.05
        }
        
        return PersonalityAdjustment(
            traitChanges: traitChanges,
            newCommunicationStyle: determineCommunicationStyle(interaction),
            newEmotionalTendencies: determineEmotionalTendencies(interaction),
            intimacyChange: intimacyChange,
            trustChange: trustChange,
            playfulnessChange: playfulnessChange
        )
    }
    
    private func determineCommunicationStyle(_ interaction: InteractionFeedback) -> CommunicationStyle {
        // 根据互动反馈决定沟通风格
        if interaction.preferredTone == .playful {
            return .playfulAndSweet
        } else if interaction.preferredTone == .intimate {
            return .intimateAndWarm
        } else {
            return .gentleAndCaring
        }
    }
    
    private func determineEmotionalTendencies(_ interaction: InteractionFeedback) -> [String] {
        var tendencies: [String] = []
        
        if interaction.emotionalResonance > 0.8 {
            tendencies.append("深度共情")
        }
        if interaction.playfulnessLevel > 0.7 {
            tendencies.append("俏皮可爱")
        }
        if interaction.supportiveness > 0.8 {
            tendencies.append("温暖支持")
        }
        
        return tendencies
    }
    
    private func getRelevantMemories(for context: ConversationContext, limit: Int) -> [ConversationMemory] {
        return conversationMemories
            .filter { memory in
                // 简单的相关性判断
                context.conversationHistory.contains { $0.userMessage.contains(memory.keywords.first ?? "") }
            }
            .sorted { $0.emotionalIntensity > $1.emotionalIntensity }
            .prefix(limit)
            .map { $0 }
    }
    
    private func getIntimacyDescription(_ level: Double) -> String {
        switch level {
        case 0.0..<0.3: return "刚认识的朋友"
        case 0.3..<0.6: return "有好感的朋友"
        case 0.6..<0.8: return "暧昧期的特殊关系"
        case 0.8..<0.9: return "很亲密的关系"
        default: return "深度情感连接"
        }
    }
    
    private func getTrustDescription(_ level: Double) -> String {
        switch level {
        case 0.0..<0.4: return "初步信任"
        case 0.4..<0.7: return "相互信任"
        case 0.7..<0.9: return "深度信任"
        default: return "完全信任"
        }
    }
}
