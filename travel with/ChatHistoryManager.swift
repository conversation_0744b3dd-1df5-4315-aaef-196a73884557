//
//  ChatHistoryManager.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import Foundation
import SwiftData
import SwiftUI

// MARK: - 对话历史数据模型
@Model
class ChatHistory {
    @Attribute(.unique) var id: UUID
    var content: String
    var isFromUser: Bool
    var timestamp: Date
    var messageType: String // "text", "image", "emoji", "system"
    var imageDescription: String? // 图像的简短描述，格式：[图像]:一个什么图像
    var sessionId: String // 会话ID，用于分组对话
    
    init(id: UUID = UUID(), content: String, isFromUser: Bool, messageType: String, imageDescription: String? = nil, sessionId: String = "default") {
        self.id = id
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = Date()
        self.messageType = messageType
        self.imageDescription = imageDescription
        self.sessionId = sessionId
    }
    
    // 转换为ChatMessage用于显示，保持原有的ID
    func toChatMessage() -> ChatMessage {
        let displayContent = imageDescription ?? content
        let type = ChatMessage.MessageType(rawValue: messageType) ?? .text
        return ChatMessage(
            id: self.id,
            content: displayContent,
            isFromUser: isFromUser,
            messageType: type,
            timestamp: timestamp
        )
    }
    
    // 用于RAG的简化文本
    var ragText: String {
        let sender = isFromUser ? "用户" : "AI"
        let displayContent = imageDescription ?? content
        return "\(sender): \(displayContent)"
    }
}

// MARK: - 对话历史管理器
@MainActor
class ChatHistoryManager: ObservableObject {
    private var modelContext: ModelContext?
    
    @Published var recentMessages: [ChatHistory] = []
    @Published var isLoading = false
    @Published var hasMoreHistory = true
    
    private let pageSize = 20 // 首次加载20条
    private let loadMoreSize = 10 // 上翻加载10条
    private var currentOffset = 0
    
    init() {
        setupModelContext()
    }
    
    private func setupModelContext() {
        do {
            // 为ChatHistory创建独立的数据库文件，避免与其他实体冲突
            let url = URL.applicationSupportDirectory.appending(path: "ChatHistory.store")

            let config = ModelConfiguration(
                "ChatHistory",
                schema: Schema([ChatHistory.self]),
                url: url
            )

            let container = try ModelContainer(for: ChatHistory.self, configurations: config)
            self.modelContext = ModelContext(container)

            print("✅ ChatHistory数据库初始化成功，存储位置: \(url.path)")
        } catch {
            print("❌ 初始化ChatHistory数据库失败: \(error)")
            print("❌ 错误详情: \(error.localizedDescription)")

            // 如果失败，尝试删除旧数据库文件并重新创建
            do {
                let url = URL.applicationSupportDirectory.appending(path: "ChatHistory.store")
                if FileManager.default.fileExists(atPath: url.path) {
                    try FileManager.default.removeItem(at: url)
                    print("🗑️ 已删除损坏的数据库文件")
                }

                let config = ModelConfiguration(
                    "ChatHistory",
                    schema: Schema([ChatHistory.self]),
                    url: url
                )

                let container = try ModelContainer(for: ChatHistory.self, configurations: config)
                self.modelContext = ModelContext(container)
                print("✅ ChatHistory数据库重新创建成功")
            } catch {
                print("❌ 重新创建数据库也失败，使用内存存储: \(error)")

                // 最后的备用方案：内存存储
                do {
                    let memoryConfig = ModelConfiguration(
                        "ChatHistoryMemory",
                        schema: Schema([ChatHistory.self]),
                        isStoredInMemoryOnly: true
                    )
                    let memoryContainer = try ModelContainer(for: ChatHistory.self, configurations: memoryConfig)
                    self.modelContext = ModelContext(memoryContainer)
                    print("⚠️ 使用内存存储作为备用方案")
                } catch {
                    print("❌ 内存存储也失败: \(error)")
                }
            }
        }
    }
    
    // MARK: - 保存消息到历史记录
    func saveMessage(_ message: ChatMessage, imageDescription: String? = nil, sessionId: String = "default") {
        guard let context = modelContext else { return }
        
        let history = ChatHistory(
            content: message.content,
            isFromUser: message.isFromUser,
            messageType: message.messageType.rawValue,
            imageDescription: imageDescription,
            sessionId: sessionId
        )
        
        context.insert(history)
        
        do {
            try context.save()
            print("✅ 消息已保存到历史记录")
        } catch {
            print("❌ 保存消息失败: \(error)")
        }
    }
    
    // MARK: - 检查数据库状态
    func checkDatabaseStatus() -> Bool {
        guard let context = modelContext else {
            print("❌ ModelContext未初始化")
            return false
        }

        do {
            // 尝试执行一个简单的查询来测试数据库连接
            var descriptor = FetchDescriptor<ChatHistory>(
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            descriptor.fetchLimit = 1

            let _ = try context.fetch(descriptor)
            print("✅ 数据库连接正常")
            return true
        } catch {
            print("❌ 数据库连接失败: \(error)")
            print("❌ 错误详情: \(error.localizedDescription)")

            // 尝试重新初始化
            setupModelContext()
            return false
        }
    }

    // MARK: - 加载最近的对话记录
    func loadRecentMessages(sessionId: String = "default") async {
        guard checkDatabaseStatus() else {
            print("❌ 数据库状态检查失败，跳过加载")
            return
        }

        guard let context = modelContext else { return }

        isLoading = true
        currentOffset = 0

        do {
            var descriptor = FetchDescriptor<ChatHistory>(
                predicate: #Predicate { $0.sessionId == sessionId },
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            descriptor.fetchLimit = pageSize

            let histories = try context.fetch(descriptor)
            recentMessages = histories.reversed() // 按时间正序显示
            hasMoreHistory = histories.count == pageSize
            currentOffset = histories.count

            print("✅ 加载了 \(histories.count) 条最近消息")
        } catch {
            print("❌ 加载最近消息失败: \(error)")
            print("❌ 错误详情: \(error.localizedDescription)")
        }

        isLoading = false
    }
    
    // MARK: - 加载更多历史记录
    func loadMoreHistory(sessionId: String = "default") async {
        guard hasMoreHistory, !isLoading else {
            print("⚠️ 没有更多历史记录或正在加载中")
            return
        }

        guard checkDatabaseStatus() else {
            print("❌ 数据库状态检查失败，跳过加载更多历史")
            return
        }

        guard let context = modelContext else { return }

        isLoading = true

        do {
            var descriptor = FetchDescriptor<ChatHistory>(
                predicate: #Predicate { $0.sessionId == sessionId },
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            descriptor.fetchLimit = loadMoreSize
            descriptor.fetchOffset = currentOffset

            let histories = try context.fetch(descriptor)

            if histories.isEmpty {
                hasMoreHistory = false
                print("⚠️ 没有更多历史记录")
            } else {
                // 在开头插入更早的消息
                recentMessages.insert(contentsOf: histories.reversed(), at: 0)
                currentOffset += histories.count
                hasMoreHistory = histories.count == loadMoreSize
                print("✅ 加载了 \(histories.count) 条更多历史消息，当前总数: \(recentMessages.count)")
            }
        } catch {
            print("❌ 加载更多历史失败: \(error)")
            print("❌ 错误详情: \(error.localizedDescription)")
        }

        isLoading = false
    }
    
    // MARK: - 获取RAG上下文
    func getRAGContext(sessionId: String = "default", maxMessages: Int = 10) async -> String {
        guard let context = modelContext else { return "" }
        
        do {
            var descriptor = FetchDescriptor<ChatHistory>(
                predicate: #Predicate { $0.sessionId == sessionId },
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            descriptor.fetchLimit = maxMessages
            
            let histories = try context.fetch(descriptor)
            let ragTexts = histories.reversed().map { $0.ragText }
            
            return ragTexts.joined(separator: "\n")
        } catch {
            print("❌ 获取RAG上下文失败: \(error)")
            return ""
        }
    }
    

    
    // MARK: - 获取所有会话ID
    func getAllSessionIds() async -> [String] {
        guard let context = modelContext else { return [] }
        
        do {
            let descriptor = FetchDescriptor<ChatHistory>()
            let histories = try context.fetch(descriptor)
            let sessionIds = Set(histories.map { $0.sessionId })
            return Array(sessionIds).sorted()
        } catch {
            print("❌ 获取会话ID失败: \(error)")
            return []
        }
    }
    
    // MARK: - 获取会话统计信息
    func getSessionStats(sessionId: String = "default") async -> (messageCount: Int, lastMessageTime: Date?) {
        guard let context = modelContext else { return (0, nil) }
        
        do {
            let descriptor = FetchDescriptor<ChatHistory>(
                predicate: #Predicate { $0.sessionId == sessionId },
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            
            let histories = try context.fetch(descriptor)
            let lastMessage = histories.first
            
            return (histories.count, lastMessage?.timestamp)
        } catch {
            print("❌ 获取会话统计失败: \(error)")
            return (0, nil)
        }
    }
}

// MARK: - 预览用的模拟数据
extension ChatHistoryManager {
    static let preview: ChatHistoryManager = {
        let manager = ChatHistoryManager()
        // 模拟一些历史数据
        manager.recentMessages = [
            ChatHistory(content: "嗨！今天想去哪玩呀？😊", isFromUser: false, messageType: "text"),
            ChatHistory(content: "想去海边看看", isFromUser: true, messageType: "text"),
            ChatHistory(content: "[图像]:一张海边风景照", isFromUser: true, messageType: "image", imageDescription: "[图像]:一张海边风景照"),
            ChatHistory(content: "哇海边！天气这么好去海边绝了", isFromUser: false, messageType: "text"),
            ChatHistory(content: "对吧！正好拍照", isFromUser: true, messageType: "text")
        ]
        return manager
    }()
}
