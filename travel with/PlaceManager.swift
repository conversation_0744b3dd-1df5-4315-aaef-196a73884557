//
//  PlaceManager.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import Foundation
import SwiftData
import CoreLocation
import SwiftUI

// MARK: - 旅行计划数据模型
@Model
class TravelPlan {
    @Attribute(.unique) var id: UUID
    var name: String
    var createdDate: Date
    var places: [Place]
    
    init(name: String) {
        self.id = UUID()
        self.name = name
        self.createdDate = Date()
        self.places = []
    }
}

// MARK: - 地点数据模型
@Model
class Place {
    @Attribute(.unique) var id: UUID
    var name: String
    var address: String
    var latitude: Double
    var longitude: Double
    var coverImageURL: String?
    var notes: String
    var createdDate: Date
    var plan: TravelPlan?
    
    init(name: String, address: String, coordinate: CLLocationCoordinate2D, coverImageURL: String? = nil, notes: String = "") {
        self.id = UUID()
        self.name = name
        self.address = address
        self.latitude = coordinate.latitude
        self.longitude = coordinate.longitude
        self.coverImageURL = coverImageURL
        self.notes = notes
        self.createdDate = Date()
    }
    
    var coordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }
}

// MARK: - 地点管理器
@MainActor
class PlaceManager: ObservableObject {
    private var modelContext: ModelContext?
    
    @Published var travelPlans: [TravelPlan] = []
    @Published var selectedPlan: TravelPlan?
    @Published var isLoading = false
    
    // 默认的"全部地点"计划 - 虚拟计划，用于显示所有地点
    private var allPlacesPlan: TravelPlan {
        if let existingPlan = travelPlans.first(where: { $0.name == "全部地点" }) {
            return existingPlan
        } else {
            let plan = TravelPlan(name: "全部地点")
            // 注意：这个计划不会实际存储地点，只是作为显示所有地点的虚拟容器
            return plan
        }
    }

    // 默认计划 - 用于在"全部地点"中添加地点时的目标计划
    private var defaultPlan: TravelPlan {
        if let existingPlan = travelPlans.first(where: { $0.name == "默认计划" }) {
            return existingPlan
        } else {
            let plan = TravelPlan(name: "默认计划")
            return plan
        }
    }
    
    init() {
        setupModelContext()
        loadTravelPlans()
    }
    
    private func setupModelContext() {
        do {
            // 为地点数据创建独立的数据库文件
            let url = URL.applicationSupportDirectory.appending(path: "Places.store")

            let config = ModelConfiguration(
                "Places",
                schema: Schema([TravelPlan.self, Place.self]),
                url: url
            )

            let container = try ModelContainer(for: TravelPlan.self, Place.self, configurations: config)
            self.modelContext = ModelContext(container)

            print("✅ Places数据库初始化成功，存储位置: \(url.path)")
        } catch {
            print("❌ 初始化PlaceManager SwiftData失败: \(error)")
            print("❌ 错误详情: \(error.localizedDescription)")

            // 如果失败，尝试删除旧数据库文件并重新创建
            do {
                let url = URL.applicationSupportDirectory.appending(path: "Places.store")
                if FileManager.default.fileExists(atPath: url.path) {
                    try FileManager.default.removeItem(at: url)
                    print("🗑️ 已删除损坏的Places数据库文件")
                }

                let config = ModelConfiguration(
                    "Places",
                    schema: Schema([TravelPlan.self, Place.self]),
                    url: url
                )

                let container = try ModelContainer(for: TravelPlan.self, Place.self, configurations: config)
                self.modelContext = ModelContext(container)
                print("✅ Places数据库重新创建成功")
            } catch {
                print("❌ 重新创建Places数据库也失败: \(error)")
            }
        }
    }
    
    // MARK: - 旅行计划管理
    
    func loadTravelPlans() {
        guard let context = modelContext else { return }

        do {
            let descriptor = FetchDescriptor<TravelPlan>(
                sortBy: [SortDescriptor(\.createdDate, order: .forward)]
            )
            let plans = try context.fetch(descriptor)

            // 确保在主线程更新UI
            DispatchQueue.main.async {
                var updatedPlans = plans
                var needsSave = false

                // 确保有"全部地点"计划
                if !plans.contains(where: { $0.name == "全部地点" }) {
                    let allPlacesPlan = TravelPlan(name: "全部地点")
                    context.insert(allPlacesPlan)
                    updatedPlans.append(allPlacesPlan)
                    needsSave = true
                }

                // 确保有"默认计划"
                if !plans.contains(where: { $0.name == "默认计划" }) {
                    let defaultPlan = TravelPlan(name: "默认计划")
                    context.insert(defaultPlan)
                    updatedPlans.append(defaultPlan)
                    needsSave = true
                }

                // 如果有新计划需要保存
                if needsSave {
                    do {
                        try context.save()
                        print("✅ 创建了必要的默认计划")
                    } catch {
                        print("❌ 保存默认计划失败: \(error)")
                    }
                }

                // 排序计划：全部地点 -> 默认计划 -> 其他计划
                let allPlacesPlans = updatedPlans.filter { $0.name == "全部地点" }
                let defaultPlans = updatedPlans.filter { $0.name == "默认计划" }
                let otherPlans = updatedPlans.filter { $0.name != "全部地点" && $0.name != "默认计划" }
                    .sorted { $0.createdDate < $1.createdDate }

                self.travelPlans = allPlacesPlans + defaultPlans + otherPlans

                // 设置默认选中计划
                if self.selectedPlan == nil {
                    self.selectedPlan = self.allPlacesPlan
                }

                print("✅ 加载了 \(self.travelPlans.count) 个旅行计划")
            }
        } catch {
            DispatchQueue.main.async {
                print("❌ 加载旅行计划失败: \(error)")
            }
        }
    }
    
    func createTravelPlan(name: String) {
        guard let context = modelContext else { return }

        let newPlan = TravelPlan(name: name)
        context.insert(newPlan)

        do {
            try context.save()
            print("✅ 创建旅行计划: \(name)")

            // 在主线程重新加载所有计划以确保数据一致性和UI更新
            DispatchQueue.main.async {
                self.loadTravelPlans()
                // 强制触发UI更新
                self.objectWillChange.send()
            }
        } catch {
            print("❌ 创建旅行计划失败: \(error)")
        }
    }
    
    func deleteTravelPlan(_ plan: TravelPlan) {
        guard let context = modelContext,
              plan.name != "全部地点" && plan.name != "默认计划" else {
            print("⚠️ 不能删除系统默认计划: \(plan.name)")
            return
        }

        context.delete(plan)

        do {
            try context.save()
            print("✅ 删除旅行计划: \(plan.name)")

            // 在主线程更新UI
            DispatchQueue.main.async {
                // 如果删除的是当前选中的计划，切换到默认计划
                if self.selectedPlan?.id == plan.id {
                    self.selectedPlan = self.allPlacesPlan
                }

                // 重新加载所有计划以确保数据一致性
                self.loadTravelPlans()
                // 强制触发UI更新
                self.objectWillChange.send()
            }
        } catch {
            print("❌ 删除旅行计划失败: \(error)")
        }
    }
    
    // MARK: - 地点管理
    
    func addPlace(_ place: Place, to plan: TravelPlan? = nil) {
        guard let context = modelContext else { return }

        var targetPlan = plan ?? selectedPlan ?? allPlacesPlan

        // 如果目标计划是"全部地点"，将地点添加到"默认计划"
        if targetPlan.name == "全部地点" {
            targetPlan = defaultPlan

            // 如果默认计划不在列表中，需要先添加到列表
            if !travelPlans.contains(where: { $0.name == "默认计划" }) {
                context.insert(targetPlan)
                travelPlans.append(targetPlan)

                // 重新排序以确保正确的顺序
                let allPlacesPlans = travelPlans.filter { $0.name == "全部地点" }
                let defaultPlans = travelPlans.filter { $0.name == "默认计划" }
                let otherPlans = travelPlans.filter { $0.name != "全部地点" && $0.name != "默认计划" }
                travelPlans = allPlacesPlans + defaultPlans + otherPlans
            }
        }

        // 将地点添加到指定计划
        place.plan = targetPlan
        targetPlan.places.append(place)

        context.insert(place)

        do {
            try context.save()
            print("✅ 添加地点: \(place.name) 到计划: \(targetPlan.name)")
        } catch {
            print("❌ 添加地点失败: \(error)")
        }
    }
    
    func removePlace(_ place: Place) {
        guard let context = modelContext else { return }
        
        // 从计划中移除
        if let plan = place.plan {
            plan.places.removeAll { $0.id == place.id }
        }
        
        context.delete(place)
        
        do {
            try context.save()
            print("✅ 删除地点: \(place.name)")
        } catch {
            print("❌ 删除地点失败: \(error)")
        }
    }
    
    func getPlaces(for plan: TravelPlan? = nil) -> [Place] {
        let targetPlan = plan ?? selectedPlan ?? allPlacesPlan

        // 如果是"全部地点"计划，返回所有计划中的所有地点
        if targetPlan.name == "全部地点" {
            return getAllPlaces()
        } else {
            return targetPlan.places.sorted { $0.createdDate < $1.createdDate }
        }
    }
    
    func getAllPlaces() -> [Place] {
        guard let context = modelContext else { return [] }
        
        do {
            let descriptor = FetchDescriptor<Place>(
                sortBy: [SortDescriptor(\.createdDate, order: .reverse)]
            )
            return try context.fetch(descriptor)
        } catch {
            print("❌ 获取所有地点失败: \(error)")
            return []
        }
    }
    
    // MARK: - 搜索功能
    
    func searchPlaces(query: String) -> [Place] {
        let allPlaces = getAllPlaces()
        if query.isEmpty {
            return allPlaces
        }
        
        return allPlaces.filter { place in
            place.name.localizedCaseInsensitiveContains(query) ||
            place.address.localizedCaseInsensitiveContains(query)
        }
    }
}

// MARK: - 预览用的模拟数据
extension PlaceManager {
    static let preview: PlaceManager = {
        let manager = PlaceManager()
        
        // 模拟数据
        let defaultPlan = TravelPlan(name: "全部地点")
        let xianPlan = TravelPlan(name: "西安旅行")
        
        let place1 = Place(
            name: "大雁塔",
            address: "陕西省西安市雁塔区雁塔路",
            coordinate: CLLocationCoordinate2D(latitude: 34.2186, longitude: 108.9647)
        )
        
        let place2 = Place(
            name: "兵马俑",
            address: "陕西省西安市临潼区秦始皇帝陵博物院",
            coordinate: CLLocationCoordinate2D(latitude: 34.3853, longitude: 109.2734)
        )
        
        defaultPlan.places = [place1, place2]
        xianPlan.places = [place1, place2]
        
        manager.travelPlans = [defaultPlan, xianPlan]
        manager.selectedPlan = defaultPlan
        
        return manager
    }()
}
