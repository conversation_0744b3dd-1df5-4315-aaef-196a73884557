//
//  WelcomeView.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI
import CoreLocation
import AVFoundation
import UserNotifications

struct WelcomeView: View {
    @StateObject private var permissionManager = PermissionManager()
    @StateObject private var aiService = AIService()
    @State private var currentStep: WelcomeStep = .welcome
    @State private var isCompleted = false
    @Binding var showWelcome: Bool
    
    enum WelcomeStep: Int, CaseIterable {
        case welcome = 0
        case locationPermission = 1
        case cameraPermission = 2
        case notificationPermission = 3
        case aiConnection = 4
        case completed = 5
        
        var title: String {
            switch self {
            case .welcome: return "欢迎使用 Travel With"
            case .locationPermission: return "位置权限"
            case .cameraPermission: return "相机权限"
            case .notificationPermission: return "通知权限"
            case .aiConnection: return "连接AI朋友"
            case .completed: return "准备就绪"
            }
        }
        
        var description: String {
            switch self {
            case .welcome: return "您的AI旅行伙伴已准备好陪伴您探索世界"
            case .locationPermission: return "为了提供精准的导航和位置服务，我们需要访问您的位置信息"
            case .cameraPermission: return "AR导航功能需要使用相机来识别周围环境"
            case .notificationPermission: return "接收AI朋友的贴心提醒和旅行建议"
            case .aiConnection: return "正在连接您的AI旅行伙伴..."
            case .completed: return "一切准备就绪！开始您的旅行之旅吧"
            }
        }
        
        var icon: String {
            switch self {
            case .welcome: return "heart.circle.fill"
            case .locationPermission: return "location.circle.fill"
            case .cameraPermission: return "camera.circle.fill"
            case .notificationPermission: return "bell.circle.fill"
            case .aiConnection: return "sparkles.rectangle.stack.fill"
            case .completed: return "checkmark.circle.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .welcome: return .orange
            case .locationPermission: return .blue
            case .cameraPermission: return .purple
            case .notificationPermission: return .green
            case .aiConnection: return .pink
            case .completed: return .green
            }
        }
    }
    
    var body: some View {
        ZStack {
            // 温暖的渐变背景
            LinearGradient(
                colors: [
                    currentStep.color.opacity(0.1),
                    currentStep.color.opacity(0.05),
                    Color(.systemBackground)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 40) {
                Spacer()
                
                // 步骤指示器
                ProgressIndicatorView(currentStep: currentStep.rawValue, totalSteps: WelcomeStep.allCases.count - 1)
                
                // 主要内容
                VStack(spacing: 30) {
                    // 图标动画
                    AnimatedIconView(
                        icon: currentStep.icon,
                        color: currentStep.color,
                        isAnimating: currentStep == .aiConnection
                    )
                    
                    // 标题和描述
                    VStack(spacing: 16) {
                        Text(currentStep.title)
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)
                        
                        Text(currentStep.description)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    
                    // AI连接状态
                    if currentStep == .aiConnection {
                        AIConnectionStatusView(status: aiService.connectionStatus)
                    }
                }
                
                Spacer()
                
                // 底部按钮
                VStack(spacing: 16) {
                    if currentStep != .completed && currentStep != .aiConnection {
                        Button(action: handlePrimaryAction) {
                            Text(primaryButtonTitle)
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(currentStep.color)
                                .cornerRadius(25)
                        }
                        .disabled(isButtonDisabled)
                    }
                    
                    if currentStep == .completed {
                        Button(action: completeWelcome) {
                            Text("开始旅行")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(
                                    LinearGradient(
                                        colors: [.orange, .pink],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .cornerRadius(25)
                        }
                    }
                    
                    if currentStep != .welcome && currentStep != .completed && currentStep != .aiConnection {
                        Button("跳过") {
                            nextStep()
                        }
                        .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 30)
                .padding(.bottom, 50)
            }
        }
        .environmentObject(aiService)
        .onChange(of: aiService.connectionStatus) { _, status in
            if case .connected = status, currentStep == .aiConnection {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    nextStep()
                }
            }
        }
    }
    
    // MARK: - 主要操作
    private func handlePrimaryAction() {
        switch currentStep {
        case .welcome:
            nextStep()
        case .locationPermission:
            permissionManager.requestLocationPermission {
                nextStep()
            }
        case .cameraPermission:
            permissionManager.requestCameraPermission {
                nextStep()
            }
        case .notificationPermission:
            permissionManager.requestNotificationPermission {
                nextStep()
            }
        default:
            nextStep()
        }
    }
    
    // MARK: - 下一步
    private func nextStep() {
        withAnimation(.spring()) {
            if currentStep.rawValue < WelcomeStep.allCases.count - 1 {
                currentStep = WelcomeStep(rawValue: currentStep.rawValue + 1) ?? .completed
            }
        }
    }
    
    // MARK: - 完成欢迎流程
    private func completeWelcome() {
        UserDefaults.standard.set(true, forKey: "hasCompletedWelcome")
        withAnimation(.spring()) {
            showWelcome = false
        }
    }
    
    // MARK: - 计算属性
    private var primaryButtonTitle: String {
        switch currentStep {
        case .welcome: return "开始设置"
        case .locationPermission: return "允许位置访问"
        case .cameraPermission: return "允许相机访问"
        case .notificationPermission: return "允许通知"
        default: return "继续"
        }
    }
    
    private var isButtonDisabled: Bool {
        return currentStep == .aiConnection
    }
}

// MARK: - 进度指示器
struct ProgressIndicatorView: View {
    let currentStep: Int
    let totalSteps: Int
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(0..<totalSteps, id: \.self) { step in
                Circle()
                    .fill(step <= currentStep ? Color.orange : Color.gray.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .scaleEffect(step == currentStep ? 1.2 : 1.0)
                    .animation(.spring(), value: currentStep)
            }
        }
    }
}

// MARK: - 动画图标
struct AnimatedIconView: View {
    let icon: String
    let color: Color
    let isAnimating: Bool
    
    @State private var rotation: Double = 0
    @State private var scale: Double = 1.0
    
    var body: some View {
        Image(systemName: icon)
            .font(.system(size: 80))
            .foregroundStyle(
                LinearGradient(
                    colors: [color, color.opacity(0.7)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .scaleEffect(scale)
            .rotationEffect(.degrees(rotation))
            .onAppear {
                if isAnimating {
                    withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                        rotation = 360
                    }
                    withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                        scale = 1.1
                    }
                }
            }
    }
}

// MARK: - AI连接状态视图
struct AIConnectionStatusView: View {
    let status: AIService.ConnectionStatus
    
    var body: some View {
        HStack(spacing: 8) {
            if case .connecting = status {
                ProgressView()
                    .scaleEffect(0.8)
            } else {
                Circle()
                    .fill(statusColor)
                    .frame(width: 8, height: 8)
            }
            
            Text(statusText)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(20)
    }
    
    private var statusColor: Color {
        switch status {
        case .connected: return .green
        case .connecting: return .orange
        case .disconnected, .error: return .red
        }
    }
    
    private var statusText: String {
        switch status {
        case .connected: return "AI朋友已连接"
        case .connecting: return "正在连接..."
        case .disconnected: return "连接失败"
        case .error(let message): return "错误: \(message)"
        }
    }
}

// MARK: - 权限状态枚举
enum PermissionStatus {
    case notDetermined
    case granted
    case denied
    case restricted
}

// MARK: - 权限管理器
@MainActor
class PermissionManager: ObservableObject {
    @Published var locationPermissionStatus: CLAuthorizationStatus = .notDetermined
    @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
    @Published var notificationPermissionStatus: UNAuthorizationStatus = .notDetermined
    @Published var microphonePermissionStatus: PermissionStatus = .notDetermined
    @Published var allVoicePermissionsGranted: Bool = false
    @Published var showingPermissionAlert: Bool = false
    @Published var permissionAlertMessage: String = ""

    // 权限申请完成回调
    var onPermissionsCompleted: (() -> Void)?

    init() {
        checkVoicePermissions()
    }
    
    func requestLocationPermission(completion: @escaping () -> Void) {
        let manager = CLLocationManager()
        manager.requestWhenInUseAuthorization()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            completion()
        }
    }
    
    func requestCameraPermission(completion: @escaping () -> Void) {
        AVCaptureDevice.requestAccess(for: .video) { _ in
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                completion()
            }
        }
    }
    
    func requestNotificationPermission(completion: @escaping () -> Void) {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { _, _ in
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                completion()
            }
        }
    }

    // MARK: - 语音权限相关方法
    func checkVoicePermissions() {
        // 检查麦克风权限
        microphonePermissionStatus = convertAVAuthStatus(AVAudioSession.sharedInstance().recordPermission)

        updateVoicePermissionsStatus()
    }

    func requestVoicePermissions() async {
        print("🔐 开始申请麦克风权限...")

        // 申请麦克风权限
        await requestMicrophonePermission()

        // 更新总体权限状态
        updateVoicePermissionsStatus()

        // 如果权限已获得，调用完成回调
        if allVoicePermissionsGranted {
            print("✅ 麦克风权限申请完成")
            onPermissionsCompleted?()
        } else {
            print("⚠️ 麦克风权限未获得")
            showVoicePermissionDeniedAlert()
        }
    }

    private func requestMicrophonePermission() async {
        print("🎤 申请麦克风权限...")

        let granted = await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }

        microphonePermissionStatus = granted ? .granted : .denied
        print("🎤 麦克风权限结果: \(granted ? "已授权" : "被拒绝")")
    }



    private func updateVoicePermissionsStatus() {
        allVoicePermissionsGranted = microphonePermissionStatus == .granted
        print("📊 语音权限状态更新 - 麦克风: \(microphonePermissionStatus), 全部授权: \(allVoicePermissionsGranted)")
    }

    private func showVoicePermissionDeniedAlert() {
        if microphonePermissionStatus == .denied {
            permissionAlertMessage = """
            应用需要麦克风权限才能使用语音功能：

            请前往设置 > 隐私与安全性 > 麦克风中手动开启权限。
            """
            showingPermissionAlert = true
        }
    }

    func openSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }

    // MARK: - 权限状态转换辅助方法
    private func convertAVAuthStatus(_ status: AVAudioSession.RecordPermission) -> PermissionStatus {
        switch status {
        case .undetermined:
            return .notDetermined
        case .granted:
            return .granted
        case .denied:
            return .denied
        @unknown default:
            return .notDetermined
        }
    }


}

// MARK: - 预览
#Preview {
    WelcomeView(showWelcome: .constant(true))
}
