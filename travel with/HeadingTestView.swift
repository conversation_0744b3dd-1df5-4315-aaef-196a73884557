//
//  HeadingTestView.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI
import CoreLocation

struct HeadingTestView: View {
    @ObservedObject private var locationManager = SharedLocationManager.shared
    @State private var isTestingHeading = false

    // 角度平滑处理
    @State private var smoothedHeading: Double = 0
    @State private var lastHeading: Double = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                Text("航向测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                // 设备朝向显示
                VStack(spacing: 15) {
                    Text("设备朝向")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    ZStack {
                        // 指南针背景
                        Circle()
                            .stroke(Color.gray.opacity(0.3), lineWidth: 2)
                            .frame(width: 200, height: 200)
                        
                        // 方向标记
                        ForEach(0..<4) { index in
                            Text(["N", "E", "S", "W"][index])
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                                .offset(y: -85)
                                .rotationEffect(.degrees(Double(index * 90)))
                        }
                        
                        // 指针
                        Image(systemName: "location.north.fill")
                            .font(.title)
                            .foregroundColor(.red)
                            .rotationEffect(.degrees(locationManager.deviceHeading))
                            .animation(.easeInOut(duration: 0.3), value: locationManager.deviceHeading)
                    }
                    
                    Text("\(String(format: "%.1f", locationManager.deviceHeading))°")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                
                // 移动方向显示
                VStack(spacing: 15) {
                    Text("移动方向")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    HStack(spacing: 20) {
                        Image(systemName: "arrow.up.circle.fill")
                            .font(.title)
                            .foregroundColor(.blue)
                            .rotationEffect(.degrees(locationManager.userCourse))
                            .animation(.easeInOut(duration: 0.3), value: locationManager.userCourse)

                        Text("\(String(format: "%.1f", locationManager.userCourse))°")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                    }
                }

                // 模拟导航箭头（与地图中相同的实现）
                VStack(spacing: 15) {
                    Text("导航箭头模拟")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    ZStack {
                        Circle()
                            .fill(.blue)
                            .frame(width: 60, height: 60)
                            .overlay(
                                Circle()
                                    .stroke(.white, lineWidth: 3)
                            )
                            .overlay(
                                Image(systemName: "location.north.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)
                                    .rotationEffect(.degrees(getNavigationArrowRotation()))
                                    .animation(.easeInOut(duration: 0.5), value: smoothedHeading)
                            )
                            .shadow(radius: 4)

                        Text("\(String(format: "%.1f", getNavigationArrowRotation()))°")
                            .font(.caption)
                            .foregroundColor(.primary)
                            .offset(y: 50)
                    }
                }
                
                // 位置信息
                if let location = locationManager.userLocation {
                    VStack(spacing: 10) {
                        Text("当前位置")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        
                        Text("纬度: \(String(format: "%.6f", location.coordinate.latitude))")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        Text("经度: \(String(format: "%.6f", location.coordinate.longitude))")
                            .font(.body)
                            .foregroundColor(.primary)
                        
                        if location.speed >= 0 {
                            Text("速度: \(String(format: "%.1f", location.speed * 3.6)) km/h")
                                .font(.body)
                                .foregroundColor(.primary)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                }
                
                // 控制按钮
                Button(action: toggleHeadingTest) {
                    HStack {
                        Image(systemName: isTestingHeading ? "stop.circle.fill" : "play.circle.fill")
                        Text(isTestingHeading ? "停止测试" : "开始测试")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(isTestingHeading ? Color.red : Color.green)
                    .cornerRadius(25)
                }
                
                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
            .onAppear {
                locationManager.requestPermission()
            }
        }
    }
    
    private func toggleHeadingTest() {
        isTestingHeading.toggle()

        if isTestingHeading {
            locationManager.startLocationUpdates()
            locationManager.startUpdatingHeading()

            // 初始化角度平滑处理
            smoothedHeading = locationManager.deviceHeading
            lastHeading = locationManager.deviceHeading

            print("🧭 开始航向测试")
        } else {
            locationManager.stopUpdatingHeading()
            print("🛑 停止航向测试")
        }
    }

    // MARK: - 角度平滑处理
    private func smoothAngle(_ newAngle: Double) -> Double {
        let diff = angleDifference(from: lastHeading, to: newAngle)

        // 如果角度差异超过180度，说明发生了跳跃
        if abs(diff) > 180 {
            // 计算最短路径的角度
            let shortestDiff = diff > 0 ? diff - 360 : diff + 360
            smoothedHeading = lastHeading + shortestDiff
        } else {
            smoothedHeading = newAngle
        }

        lastHeading = newAngle
        return smoothedHeading
    }

    // 计算两个角度之间的差异
    private func angleDifference(from angle1: Double, to angle2: Double) -> Double {
        var diff = angle2 - angle1
        while diff > 180 { diff -= 360 }
        while diff < -180 { diff += 360 }
        return diff
    }

    // 模拟导航箭头旋转逻辑（与MapNavigationView中相同）
    private func getNavigationArrowRotation() -> Double {
        let rawAngle: Double

        // 优先使用移动方向，如果移动方向无效或用户静止则使用设备朝向
        if let userLocation = locationManager.userLocation,
           userLocation.course >= 0 && userLocation.speed > 0.5 { // 速度大于0.5m/s才使用移动方向
            print("🧭 测试视图使用移动方向: \(String(format: "%.1f", locationManager.userCourse))° (速度: \(String(format: "%.1f", userLocation.speed))m/s)")
            rawAngle = locationManager.userCourse
        } else {
            print("🧭 测试视图使用设备朝向: \(String(format: "%.1f", locationManager.deviceHeading))°")
            rawAngle = locationManager.deviceHeading
        }

        // 应用角度平滑处理
        let smoothedAngle = smoothAngle(rawAngle)
        print("🧭 测试视图平滑后角度: \(String(format: "%.1f", smoothedAngle))° (原始: \(String(format: "%.1f", rawAngle))°)")
        return smoothedAngle
    }
}

#Preview {
    HeadingTestView()
}
