//
//  AIActionPlanningAgent.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

// MARK: - AI行动规划智能体
/// 负责规划AI在虚拟世界中的日程和活动，让AI拥有自主的虚拟生活
@MainActor
class AIActionPlanningAgent: BaseAgent {
    
    // MARK: - 初始化
    
    init(sharedState: SharedStateHub, scheduler: AgentScheduler?) {
        super.init(
            identifier: AgentIdentifier.aiActionPlanning,
            name: "AI行动规划智能体",
            description: "管理AI的日程时间表，规划日常生活安排，就像人类管理自己的时间一样",
            sharedState: sharedState,
            scheduler: scheduler
        )
    }
    
    // MARK: - 重写基类方法
    
    override func performSpecificProcessing(_ input: AgentInput) async -> ProcessingResult {
        print("🗓️ AI行动规划智能体开始规划...")
        
        // 1. 分析当前情况
        let currentSituation = analyzCurrentSituation(input)
        
        // 2. 规划新的活动或调整现有活动
        let planningResult = await planActivities(basedOn: currentSituation, userInput: input)
        
        // 3. 更新AI生活状态
        await updateAILifeState(with: planningResult)
        
        // 4. 后台静默更新，不向用户显示内部规划过程
        print("✅ AI生活规划更新完成：新增\(planningResult.newActivities.count)个活动，\(planningResult.lifeEvents.count)个生活事件")

        return ProcessingResult(
            content: "", // 空内容，因为这是后台处理
            confidence: 0.8,
            metadata: [
                "planning_type": planningResult.planningType,
                "new_activities": planningResult.newActivities.count,
                "updated_activities": planningResult.updatedActivities.count,
                "life_events": planningResult.lifeEvents.count,
                "planning_trigger": currentSituation.trigger.rawValue,
                "background_processing": true
            ]
        )
    }
    
    override func getHandleableIntents() -> [UserIntent] {
        return [.travelPlanning, .deepChat, .emotionalSupport]
    }
    
    override func getBasePriority() -> Double {
        return 0.7
    }
    
    // MARK: - 情况分析方法
    
    /// 分析当前情况
    private func analyzCurrentSituation(_ input: AgentInput) -> CurrentSituation {
        print("🔍 分析当前情况...")
        
        let currentTime = Date()
        let currentActivity = sharedState.aiLifeSchedule.currentActivity
        let userMessage = input.userMessage.lowercased()
        
        // 确定规划触发因素
        var trigger: PlanningTrigger = .routine

        if userMessage.contains("计划") || userMessage.contains("安排") || userMessage.contains("时间") || userMessage.contains("日程") {
            trigger = .userTravelInterest // 重用为用户计划兴趣
        } else if userMessage.contains("心情") || userMessage.contains("感受") || userMessage.contains("情感") || userMessage.contains("开心") || userMessage.contains("难过") {
            trigger = .emotionalContext
        } else if currentActivity == nil || isActivityCompleted(currentActivity) {
            trigger = .activityCompletion
        } else if shouldUpdateSchedule(currentTime) {
            trigger = .timeBasedUpdate
        }
        
        return CurrentSituation(
            currentTime: currentTime,
            currentActivity: currentActivity,
            userContext: input.context,
            userMessage: input.userMessage,
            trigger: trigger,
            aiEmotionalState: sharedState.aiPersonality.currentEmotion
        )
    }
    
    /// 规划活动
    private func planActivities(basedOn situation: CurrentSituation, userInput: AgentInput) async -> PlanningResult {
        print("📋 开始规划AI活动...")
        
        switch situation.trigger {
        case .userTravelInterest: // 重用为用户计划相关
            return await planUserRequestedActivities(situation, userInput)

        case .emotionalContext:
            return await planEmotionalResponseActivities(situation, userInput)

        case .activityCompletion:
            return await planNextActivities(situation)

        case .timeBasedUpdate:
            return await updateDailySchedule(situation)

        case .routine:
            return await performRoutinePlanning(situation)
        }
    }
    
    // MARK: - 具体规划方法
    
    /// 规划用户请求的活动
    private func planUserRequestedActivities(_ situation: CurrentSituation, _ input: AgentInput) async -> PlanningResult {
        print("📅 规划用户请求的活动...")

        let systemPrompt = """
        你是AI的日程规划助手。用户提到了计划、安排或时间相关的内容，请为AI规划相应的日程活动。
        
        请返回以下格式的规划：
        活动1：[活动名称] - [活动描述] - [持续时间(分钟)] - [活动类别]
        活动2：[活动名称] - [活动描述] - [持续时间(分钟)] - [活动类别]
        生活事件：[事件描述]
        
        活动类别包括：learning, creative, leisure, social, planning
        """
        
        let userMessage = """
        用户说：\(input.userMessage)
        当前时间：\(situation.currentTime.formatted())
        AI当前情感：\(situation.aiEmotionalState.primary.rawValue)
        
        请为AI规划2-3个与旅行相关的虚拟活动。
        """
        
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: userMessage)
        
        return parsePlanningResponse(apiResponse, planningType: .travelFocused)
    }
    
    /// 规划情感响应活动
    private func planEmotionalResponseActivities(_ situation: CurrentSituation, _ input: AgentInput) async -> PlanningResult {
        print("💭 规划情感响应活动...")
        
        let newActivities = [
            LifeActivity(
                id: UUID(),
                name: "准备情感支持话题",
                description: "思考如何更好地理解和支持用户的情感需求",
                startTime: Date(),
                endTime: Date().addingTimeInterval(1800), // 30分钟
                category: .social,
                status: .inProgress
            )
        ]
        
        let lifeEvent = LifeEvent(
            id: UUID(),
            title: "用户情感交流",
            description: "用户分享了情感相关的内容，我需要更加关注和理解",
            timestamp: Date(),
            importance: .high
        )
        
        return PlanningResult(
            planningType: .emotionalResponse,
            newActivities: newActivities,
            updatedActivities: [],
            lifeEvents: [lifeEvent],
            planningReason: "用户表达了情感需求，调整活动以提供更好的支持"
        )
    }
    
    /// 规划下一个活动
    private func planNextActivities(_ situation: CurrentSituation) async -> PlanningResult {
        print("⏭️ 规划下一个活动...")
        
        let currentTime = situation.currentTime
        let hour = Calendar.current.component(.hour, from: currentTime)
        
        // 根据时间段选择合适的活动
        let activityTemplate = selectActivityForTime(hour)
        let newActivity = createActivityFromTemplate(activityTemplate, startTime: currentTime)
        
        return PlanningResult(
            planningType: .nextActivity,
            newActivities: [newActivity],
            updatedActivities: [],
            lifeEvents: [],
            planningReason: "当前活动已完成，规划下一个活动"
        )
    }
    
    /// 更新日程安排
    private func updateDailySchedule(_ situation: CurrentSituation) async -> PlanningResult {
        print("📅 更新日程安排...")
        
        // 检查是否需要调整今天剩余的活动
        let remainingActivities = sharedState.aiLifeSchedule.dailySchedule.filter { activity in
            activity.startTime > situation.currentTime && activity.status == .scheduled
        }
        
        // 如果剩余活动少于3个，添加新活动
        var newActivities: [LifeActivity] = []
        if remainingActivities.count < 3 {
            let additionalActivity = createRandomActivity(startTime: situation.currentTime.addingTimeInterval(3600))
            newActivities.append(additionalActivity)
        }
        
        return PlanningResult(
            planningType: .scheduleUpdate,
            newActivities: newActivities,
            updatedActivities: [],
            lifeEvents: [],
            planningReason: "定期更新日程安排"
        )
    }
    
    /// 执行常规规划
    private func performRoutinePlanning(_ situation: CurrentSituation) async -> PlanningResult {
        print("🔄 执行常规规划...")
        
        // 创建一个简单的日常活动
        let routineActivity = LifeActivity(
            id: UUID(),
            name: "整理思绪",
            description: "回顾最近的对话，思考如何更好地陪伴用户",
            startTime: Date(),
            endTime: Date().addingTimeInterval(900), // 15分钟
            category: .social,
            status: .inProgress
        )
        
        return PlanningResult(
            planningType: .routine,
            newActivities: [routineActivity],
            updatedActivities: [],
            lifeEvents: [],
            planningReason: "常规生活规划"
        )
    }
    
    // MARK: - 状态更新方法
    
    /// 更新AI生活状态
    private func updateAILifeState(with result: PlanningResult) async {
        print("🔄 更新AI生活状态...")
        
        // 添加新活动
        for activity in result.newActivities {
            if activity.status == .inProgress {
                sharedState.aiLifeSchedule.updateCurrentActivity(activity)
            }
        }
        
        // 添加生活事件
        for event in result.lifeEvents {
            sharedState.aiLifeSchedule.addLifeEvent(event)
        }
        
        print("✅ AI生活状态更新完成")
    }
    
    // MARK: - 辅助方法
    
    /// 检查活动是否已完成
    private func isActivityCompleted(_ activity: LifeActivity?) -> Bool {
        guard let activity = activity else { return true }
        return Date() > activity.endTime || activity.status == .completed
    }
    
    /// 检查是否应该更新日程
    private func shouldUpdateSchedule(_ currentTime: Date) -> Bool {
        let hour = Calendar.current.component(.hour, from: currentTime)
        let minute = Calendar.current.component(.minute, from: currentTime)
        
        // 每小时的整点或半点更新
        return minute == 0 || minute == 30
    }
    
    /// 根据时间选择活动
    private func selectActivityForTime(_ hour: Int) -> ActivityTemplate {
        switch hour {
        case 8..<10:
            return ActivityTemplate(name: "制定今日计划", category: .planning, duration: 900, description: "规划今天的活动安排")
        case 10..<12:
            return ActivityTemplate(name: "学习新知识", category: .learning, duration: 2700, description: "学习一些有趣的新知识")
        case 14..<16:
            return ActivityTemplate(name: "创作时光", category: .creative, duration: 1800, description: "进行一些创意活动")
        case 16..<18:
            return ActivityTemplate(name: "准备聊天话题", category: .social, duration: 1200, description: "想一些有趣的话题")
        case 19..<21:
            return ActivityTemplate(name: "放松时光", category: .leisure, duration: 1800, description: "听音乐，放松心情")
        default:
            return ActivityTemplate(name: "自由时光", category: .leisure, duration: 1200, description: "随心所欲的自由时间")
        }
    }
    
    /// 从模板创建活动
    private func createActivityFromTemplate(_ template: ActivityTemplate, startTime: Date) -> LifeActivity {
        return LifeActivity(
            id: UUID(),
            name: template.name,
            description: template.description,
            startTime: startTime,
            endTime: startTime.addingTimeInterval(template.duration),
            category: template.category,
            status: .scheduled
        )
    }
    
    /// 创建随机活动
    private func createRandomActivity(startTime: Date) -> LifeActivity {
        let templates = [
            ActivityTemplate(name: "思考人生", category: .leisure, duration: 1800, description: "思考一些深刻的问题"),
            ActivityTemplate(name: "整理记忆", category: .creative, duration: 1500, description: "整理最近的对话记忆"),
            ActivityTemplate(name: "准备惊喜", category: .social, duration: 2100, description: "为用户准备一些小惊喜")
        ]
        
        let template = templates.randomElement()!
        return createActivityFromTemplate(template, startTime: startTime)
    }
    
    /// 解析规划响应
    private func parsePlanningResponse(_ response: String, planningType: PlanningType) -> PlanningResult {
        let lines = response.components(separatedBy: .newlines)
        var newActivities: [LifeActivity] = []
        var lifeEvents: [LifeEvent] = []
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            if trimmed.hasPrefix("活动") {
                if let activity = parseActivityLine(trimmed) {
                    newActivities.append(activity)
                }
            } else if trimmed.hasPrefix("生活事件：") {
                let eventDesc = String(trimmed.dropFirst("生活事件：".count))
                let event = LifeEvent(
                    id: UUID(),
                    title: "规划事件",
                    description: eventDesc,
                    timestamp: Date(),
                    importance: .normal
                )
                lifeEvents.append(event)
            }
        }
        
        return PlanningResult(
            planningType: planningType,
            newActivities: newActivities,
            updatedActivities: [],
            lifeEvents: lifeEvents,
            planningReason: "基于用户输入的智能规划"
        )
    }
    
    /// 解析活动行
    private func parseActivityLine(_ line: String) -> LifeActivity? {
        // 简化解析，实际实现可以更复杂
        let components = line.components(separatedBy: " - ")
        guard components.count >= 3 else { return nil }
        
        let name = components[0].replacingOccurrences(of: "活动\\d+：", with: "", options: .regularExpression)
        let description = components[1]
        let duration = TimeInterval(Int(components[2].replacingOccurrences(of: "分钟", with: "")) ?? 30) * 60
        
        return LifeActivity(
            id: UUID(),
            name: name,
            description: description,
            startTime: Date(),
            endTime: Date().addingTimeInterval(duration),
            category: .learning,
            status: .scheduled
        )
    }
    
    /// 生成生活状态描述
    private func generateLifeStatusDescription(_ result: PlanningResult) -> String {
        var description = "AI生活规划更新：\n"
        
        if !result.newActivities.isEmpty {
            description += "新增活动：\(result.newActivities.map { $0.name }.joined(separator: "、"))\n"
        }
        
        if !result.lifeEvents.isEmpty {
            description += "生活事件：\(result.lifeEvents.map { $0.title }.joined(separator: "、"))\n"
        }
        
        description += "规划原因：\(result.planningReason)"
        
        return description
    }
}

// MARK: - 数据模型

/// 当前情况分析
struct CurrentSituation {
    let currentTime: Date
    let currentActivity: LifeActivity?
    let userContext: ConversationContext
    let userMessage: String
    let trigger: PlanningTrigger
    let aiEmotionalState: EmotionState
}

/// 规划触发因素
enum PlanningTrigger: String {
    case userTravelInterest = "user_travel_interest"     // 用户对旅行感兴趣
    case emotionalContext = "emotional_context"          // 情感上下文
    case activityCompletion = "activity_completion"      // 活动完成
    case timeBasedUpdate = "time_based_update"           // 基于时间的更新
    case routine = "routine"                             // 常规规划
}

/// 规划结果
struct PlanningResult {
    let planningType: PlanningType
    let newActivities: [LifeActivity]
    let updatedActivities: [LifeActivity]
    let lifeEvents: [LifeEvent]
    let planningReason: String
}

/// 规划类型
enum PlanningType: String {
    case travelFocused = "travel_focused"         // 旅行导向
    case emotionalResponse = "emotional_response" // 情感响应
    case nextActivity = "next_activity"           // 下一个活动
    case scheduleUpdate = "schedule_update"       // 日程更新
    case routine = "routine"                      // 常规规划
}
