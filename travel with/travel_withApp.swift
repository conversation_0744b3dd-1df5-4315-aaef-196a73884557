//
//  travel_withApp.swift
//  travel with
//
//  Created by <PERSON><PERSON> on 2025/7/29.
//

import SwiftUI
import SwiftData

@main
struct travel_withApp: App {
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Item.self,
            ChatHistory.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        WindowGroup {
            AppRootView()
        }
        .modelContainer(sharedModelContainer)
    }
}

// MARK: - 应用根视图
struct AppRootView: View {
    @StateObject private var permissionManager = PermissionManager()
    @State private var hasCheckedPermissions = false

    var body: some View {
        Group {
            if hasCheckedPermissions {
                if permissionManager.allVoicePermissionsGranted {
                    MainTabView()
                } else {
                    VoicePermissionView {
                        // 权限申请完成后的回调
                        permissionManager.checkVoicePermissions()
                    }
                }
            } else {
                // 启动画面
                LaunchScreenView()
                    .onAppear {
                        // 延迟检查权限，给启动画面一些显示时间
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            permissionManager.checkVoicePermissions()
                            hasCheckedPermissions = true
                        }
                    }
            }
        }
        .animation(.easeInOut(duration: 0.3), value: hasCheckedPermissions)
        .animation(.easeInOut(duration: 0.3), value: permissionManager.allVoicePermissionsGranted)
    }
}


