//
//  EmotionPerceptionAgent.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

// MARK: - 情感感知智能体
/// 负责深度分析用户和AI的情感状态，推荐合适的TTS情感音色
@MainActor
class EmotionPerceptionAgent: BaseAgent {
    
    // MARK: - 初始化
    
    init(sharedState: SharedStateHub, scheduler: AgentScheduler?) {
        super.init(
            identifier: AgentIdentifier.emotionPerception,
            name: "情感感知智能体",
            description: "深度分析用户情感状态，管理AI情感反应，推荐TTS音色",
            sharedState: sharedState,
            scheduler: scheduler
        )
    }
    
    // MARK: - 重写基类方法
    
    override func performSpecificProcessing(_ input: AgentInput) async -> ProcessingResult {
        print("💭 情感感知智能体开始分析情感...")
        
        // 1. 深度分析用户情感
        let userEmotionAnalysis = await analyzeUserEmotionDeep(input)
        
        // 2. 确定AI情感反应
        let aiEmotionResponse = await determineAIEmotionResponse(
            userEmotion: userEmotionAnalysis.emotion,
            context: input.context,
            userMessage: input.userMessage
        )
        
        // 3. 推荐TTS情感音色
        let recommendedTTS = recommendOptimalTTSEmotion(
            aiEmotion: aiEmotionResponse,
            userEmotion: userEmotionAnalysis.emotion,
            context: input.context
        )
        
        // 4. 生成情感感知报告
        let emotionReport = generateEmotionReport(
            userAnalysis: userEmotionAnalysis,
            aiEmotion: aiEmotionResponse,
            ttsRecommendation: recommendedTTS
        )
        
        return ProcessingResult(
            content: emotionReport,
            confidence: userEmotionAnalysis.confidence,
            emotionRecommendation: aiEmotionResponse,
            ttsEmotion: recommendedTTS,
            metadata: [
                "user_emotion": userEmotionAnalysis.emotion.primary.rawValue,
                "user_emotion_intensity": userEmotionAnalysis.emotion.intensity,
                "ai_emotion": aiEmotionResponse.primary.rawValue,
                "ai_emotion_intensity": aiEmotionResponse.intensity,
                "tts_recommendation": recommendedTTS.rawValue,
                "analysis_confidence": userEmotionAnalysis.confidence,
                "emotion_triggers": userEmotionAnalysis.triggers
            ]
        )
    }
    
    override func getHandleableIntents() -> [UserIntent] {
        return [.emotionalSupport, .deepChat, .simpleChat]
    }
    
    override func getBasePriority() -> Double {
        return 0.9
    }
    
    // MARK: - 深度情感分析方法
    
    /// 深度分析用户情感
    private func analyzeUserEmotionDeep(_ input: AgentInput) async -> UserEmotionAnalysis {
        print("🔍 开始深度用户情感分析...")
        
        // 构建情感分析系统提示词
        let systemPrompt = generateEmotionAnalysisPrompt()
        
        // 准备分析消息
        let analysisMessage = prepareEmotionAnalysisMessage(input)
        
        // 调用API进行情感分析
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: analysisMessage)
        
        // 解析情感分析结果
        return parseEmotionAnalysisResult(apiResponse, originalMessage: input.userMessage)
    }
    
    /// 生成情感分析系统提示词
    private func generateEmotionAnalysisPrompt() -> String {
        return """
        你是小旅的情感感知系统，专门分析和用户（你的暧昧对象）的情感状态。你需要像一个敏感细腻的女生一样，深度理解用户的情感和心理需求。

        分析背景：
        - 你和用户正处在暧昧期，关系特殊而微妙
        - 需要特别关注用户对你的情感态度变化
        - 要敏锐察觉用户是否需要关怀、撒娇、还是深度交流
        - 分析用户是否在试探你们的关系进展

        请分析用户的情感状态，并返回以下格式的结果：

        主要情感：[情感类型]
        情感强度：[0.0-1.0的数值]
        次要情感：[次要情感列表，用逗号分隔]
        情感触发因素：[导致这种情感的原因]
        关系信号：[用户对你们关系的态度暗示]
        需要的回应类型：[温柔关怀/俏皮撒娇/深度理解/暧昧调情]
        建议TTS音色：[最适合的情感音色]

        情感类型包括：
        - happy: 开心、愉悦（可能想和你分享快乐）
        - sad: 悲伤、失落（需要你的安慰和陪伴）
        - excited: 兴奋、激动（想要你的回应和共鸣）
        - lonely: 孤独、寂寞（渴望你的关注和陪伴）
        - curious: 好奇、探索（对你或你们的关系感兴趣）
        - flirty: 调情、暧昧（在试探或推进你们的关系）
        - caring: 关心、在意（对你表现出特殊关爱）
        - playful: 俏皮、调皮（想要和你互动玩耍）
        - intimate: 亲密、深情（想要更深层的情感连接）
        - protective: 占有、保护（对你表现出占有欲）

        请特别关注暧昧关系中的微妙情感信号。
        """
    }
    
    /// 准备情感分析消息
    private func prepareEmotionAnalysisMessage(_ input: AgentInput) -> String {
        var message = "用户当前消息：\(input.userMessage)\n"
        
        // 添加消息类型上下文
        switch input.messageType {
        case .emoji:
            message += "消息类型：表情符号\n"
        case .voice:
            message += "消息类型：语音消息\n"
        case .image:
            message += "消息类型：图片消息\n"
        default:
            message += "消息类型：文本消息\n"
        }
        
        // 添加对话历史上下文
        let recentHistory = input.context.conversationHistory.suffix(5)
        if !recentHistory.isEmpty {
            message += "\n对话历史上下文：\n"
            for (index, historyMsg) in recentHistory.enumerated() {
                message += "\(index + 1). 用户：\(historyMsg.userMessage)\n"
                if let aiResponse = historyMsg.aiResponse {
                    message += "   AI：\(aiResponse)\n"
                }
            }
        }
        
        // 添加时间上下文
        let timeContext = generateTimeContext()
        message += "\n时间上下文：\(timeContext)\n"
        
        message += "\n请分析用户的情感状态。"
        
        return message
    }
    
    /// 解析情感分析结果
    private func parseEmotionAnalysisResult(_ response: String, originalMessage: String) -> UserEmotionAnalysis {
        let lines = response.components(separatedBy: .newlines)
        
        var primaryEmotion: EmotionType = .curious
        var intensity: Double = 0.5
        var secondaryEmotions: [EmotionType] = []
        var triggers: [String] = []
        var supportNeeds: [String] = []
        var confidence: Double = 0.7
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            if trimmed.hasPrefix("主要情感：") {
                let emotionStr = String(trimmed.dropFirst("主要情感：".count)).trimmingCharacters(in: .whitespacesAndNewlines)
                primaryEmotion = parseEmotionType(emotionStr)
            } else if trimmed.hasPrefix("情感强度：") {
                let intensityStr = String(trimmed.dropFirst("情感强度：".count)).trimmingCharacters(in: .whitespacesAndNewlines)
                intensity = Double(intensityStr) ?? 0.5
            } else if trimmed.hasPrefix("次要情感：") {
                let emotionsStr = String(trimmed.dropFirst("次要情感：".count)).trimmingCharacters(in: .whitespacesAndNewlines)
                secondaryEmotions = emotionsStr.components(separatedBy: "，").compactMap { parseEmotionType($0.trimmingCharacters(in: .whitespacesAndNewlines)) }
            } else if trimmed.hasPrefix("情感触发因素：") {
                let triggersStr = String(trimmed.dropFirst("情感触发因素：".count)).trimmingCharacters(in: .whitespacesAndNewlines)
                triggers = [triggersStr]
            } else if trimmed.hasPrefix("支持需求：") {
                let needsStr = String(trimmed.dropFirst("支持需求：".count)).trimmingCharacters(in: .whitespacesAndNewlines)
                supportNeeds = [needsStr]
            }
        }
        
        // 基于分析质量调整置信度
        if !triggers.isEmpty && !supportNeeds.isEmpty {
            confidence = 0.9
        } else if triggers.isEmpty && supportNeeds.isEmpty {
            confidence = 0.6
        }
        
        let emotion = EmotionState(
            primary: primaryEmotion,
            intensity: intensity,
            secondary: secondaryEmotions,
            duration: estimateEmotionDuration(primaryEmotion, intensity: intensity)
        )
        
        print("💭 用户情感分析完成: \(primaryEmotion.rawValue) (强度: \(intensity))")
        
        return UserEmotionAnalysis(
            emotion: emotion,
            confidence: confidence,
            triggers: triggers,
            supportNeeds: supportNeeds,
            analysisTimestamp: Date()
        )
    }
    
    /// 确定AI情感反应
    private func determineAIEmotionResponse(userEmotion: EmotionState, context: ConversationContext, userMessage: String) async -> EmotionState {
        print("🎭 确定AI情感反应...")
        
        // 基于用户情感确定AI的情感反应
        switch userEmotion.primary {
        case .happy:
            return EmotionState(primary: .happy, intensity: min(userEmotion.intensity + 0.1, 1.0), secondary: [.excited, .friendly])
            
        case .concerned:
            return EmotionState(primary: .empathetic, intensity: 0.8, secondary: [.caring, .helpful])
            
        case .curious:
            return EmotionState(primary: .curious, intensity: 0.7, secondary: [.helpful, .friendly])
            
        case .calm:
            return EmotionState(primary: .calm, intensity: 0.6, secondary: [.friendly])
            
        case .playful:
            return EmotionState(primary: .playful, intensity: 0.8, secondary: [.happy, .friendly])
            
        case .thoughtful:
            return EmotionState(primary: .thoughtful, intensity: 0.7, secondary: [.helpful, .curious])
            
        case .caring:
            return EmotionState(primary: .caring, intensity: 0.8, secondary: [.empathetic, .helpful])
            
        case .helpful:
            return EmotionState(primary: .helpful, intensity: 0.8, secondary: [.friendly, .curious])
            
        case .friendly:
            return EmotionState(primary: .friendly, intensity: 0.7, secondary: [.helpful, .happy])
            
        case .excited:
            return EmotionState(primary: .excited, intensity: min(userEmotion.intensity, 0.9), secondary: [.happy, .curious])
            
        case .empathetic:
            return EmotionState(primary: .empathetic, intensity: 0.8, secondary: [.caring, .helpful])
        }
    }
    
    /// 推荐最优TTS情感音色
    private func recommendOptimalTTSEmotion(aiEmotion: EmotionState, userEmotion: EmotionState, context: ConversationContext) -> CanCanEmotion {
        print("🎵 推荐TTS情感音色...")
        
        // 综合考虑AI情感、用户情感和对话上下文
        let aiPrimary = aiEmotion.primary
        let userPrimary = userEmotion.primary
        let intensity = aiEmotion.intensity
        
        // 特殊情况优先处理
        if userPrimary == .concerned && intensity > 0.7 {
            return .comfort
        }
        
        if userPrimary == .happy && aiPrimary == .happy && intensity > 0.8 {
            return .happy
        }
        
        // 根据AI主要情感推荐（优化版本，避免重音问题）
        switch aiPrimary {
        case .happy: return intensity > 0.7 ? CanCanEmotion.happy : CanCanEmotion.pleased
        case .excited: return CanCanEmotion.happy
        case .curious: return CanCanEmotion.pleased
        case .calm: return CanCanEmotion.pleased        // 改为pleased，避免yoga的特殊语调
        case .concerned: return CanCanEmotion.comfort
        case .empathetic: return CanCanEmotion.comfort
        case .caring: return CanCanEmotion.comfort
        case .playful: return CanCanEmotion.pleased     // 改为pleased，避免loveyDovey的夸张语调
        case .thoughtful: return CanCanEmotion.pleased  // 改为pleased，避免professional的过于正式
        case .helpful: return CanCanEmotion.pleased
        case .friendly: return CanCanEmotion.pleased
        }
    }
    
    // MARK: - 辅助方法
    
    /// 解析情感类型
    private func parseEmotionType(_ emotionStr: String) -> EmotionType {
        let lowercased = emotionStr.lowercased()
        
        if lowercased.contains("happy") || lowercased.contains("开心") || lowercased.contains("愉悦") {
            return .happy
        } else if lowercased.contains("excited") || lowercased.contains("兴奋") || lowercased.contains("激动") {
            return .excited
        } else if lowercased.contains("curious") || lowercased.contains("好奇") {
            return .curious
        } else if lowercased.contains("calm") || lowercased.contains("平静") || lowercased.contains("放松") {
            return .calm
        } else if lowercased.contains("concerned") || lowercased.contains("担心") || lowercased.contains("焦虑") {
            return .concerned
        } else if lowercased.contains("empathetic") || lowercased.contains("理解") || lowercased.contains("同情") {
            return .empathetic
        } else if lowercased.contains("playful") || lowercased.contains("活泼") || lowercased.contains("顽皮") {
            return .playful
        } else if lowercased.contains("thoughtful") || lowercased.contains("深思") || lowercased.contains("思考") {
            return .thoughtful
        } else if lowercased.contains("caring") || lowercased.contains("关心") || lowercased.contains("关爱") {
            return .caring
        } else if lowercased.contains("helpful") || lowercased.contains("乐于助人") || lowercased.contains("帮助") {
            return .helpful
        } else {
            return .friendly
        }
    }
    
    /// 估算情感持续时间
    private func estimateEmotionDuration(_ emotion: EmotionType, intensity: Double) -> TimeInterval {
        let baseDuration: TimeInterval
        
        switch emotion {
        case .happy, .excited: baseDuration = 1800 // 30分钟
        case .concerned, .empathetic: baseDuration = 3600 // 1小时
        case .calm, .thoughtful: baseDuration = 2400 // 40分钟
        default: baseDuration = 1200 // 20分钟
        }
        
        return baseDuration * intensity
    }
    
    /// 生成时间上下文
    private func generateTimeContext() -> String {
        let hour = Calendar.current.component(.hour, from: Date())
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        let timeStr = formatter.string(from: Date())
        
        return "当前时间: \(timeStr), 时段: \(getTimeOfDayDescription(hour))"
    }
    
    /// 获取时间段描述
    private func getTimeOfDayDescription(_ hour: Int) -> String {
        switch hour {
        case 6..<9: return "早晨"
        case 9..<12: return "上午"
        case 12..<14: return "中午"
        case 14..<18: return "下午"
        case 18..<21: return "傍晚"
        case 21..<23: return "晚上"
        default: return "深夜"
        }
    }
    
    /// 生成情感感知报告
    private func generateEmotionReport(userAnalysis: UserEmotionAnalysis, aiEmotion: EmotionState, ttsRecommendation: CanCanEmotion) -> String {
        return """
        情感感知分析报告：
        用户情感：\(userAnalysis.emotion.primary.rawValue) (强度: \(String(format: "%.1f", userAnalysis.emotion.intensity)))
        AI情感反应：\(aiEmotion.primary.rawValue) (强度: \(String(format: "%.1f", aiEmotion.intensity)))
        推荐TTS音色：\(ttsRecommendation.description)
        分析置信度：\(String(format: "%.1f", userAnalysis.confidence))
        """
    }
}

// MARK: - 用户情感分析结果

/// 用户情感分析结果
struct UserEmotionAnalysis {
    let emotion: EmotionState           // 情感状态
    let confidence: Double              // 分析置信度
    let triggers: [String]              // 情感触发因素
    let supportNeeds: [String]          // 支持需求
    let analysisTimestamp: Date         // 分析时间戳
}
