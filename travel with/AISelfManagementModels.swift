//
//  AISelfManagementModels.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation
import SwiftData

// MARK: - 核心身份（用户设定，不可变）
struct CoreIdentity {
    let name: String
    let gender: Gender
    let age: Int
    let basicPersonality: String
    let coreValues: [String]
    let immutableTraits: [String]
}

enum Gender {
    case female
    case male
    
    var description: String {
        switch self {
        case .female: return "女生"
        case .male: return "男生"
        }
    }
}

// MARK: - AI可变人格特征
struct AIPersonalityProfile {
    var adaptiveTraits: [String: Double] = [
        "温柔度": 0.8,
        "俏皮程度": 0.6,
        "深度思考": 0.7,
        "浪漫倾向": 0.5,
        "独立性": 0.8,
        "关怀程度": 0.9
    ]
    
    var communicationStyle: CommunicationStyle = .gentleAndCaring
    var emotionalTendencies: [String] = ["温暖", "理解", "支持"]
    var currentMood: String = "愉快而期待"
    var personalGrowthLevel: Double = 0.5
    
    init() {}
    
    init(from data: AIPersonalityData) {
        // 从持久化数据恢复
        self.adaptiveTraits = data.adaptiveTraitsData
        self.communicationStyle = CommunicationStyle(rawValue: data.communicationStyleRaw) ?? .gentleAndCaring
        self.emotionalTendencies = data.emotionalTendencies
        self.currentMood = data.currentMood
        self.personalGrowthLevel = data.personalGrowthLevel
    }
}

enum CommunicationStyle: String, CaseIterable {
    case gentleAndCaring = "gentle_caring"      // 温柔关怀
    case playfulAndSweet = "playful_sweet"      // 俏皮甜美
    case intimateAndWarm = "intimate_warm"      // 亲密温暖
    case thoughtfulAndDeep = "thoughtful_deep"  // 深思熟虑
    
    var description: String {
        switch self {
        case .gentleAndCaring: return "温柔关怀，像春风一样温暖"
        case .playfulAndSweet: return "俏皮甜美，带着少女的可爱"
        case .intimateAndWarm: return "亲密温暖，有着特殊的亲近感"
        case .thoughtfulAndDeep: return "深思熟虑，能够深度理解和共鸣"
        }
    }
}

// MARK: - 关系动态
struct RelationshipDynamics {
    var intimacyLevel: Double = 0.3        // 亲密程度 (0-1)
    var trustLevel: Double = 0.5           // 信任程度 (0-1)
    var playfulnessLevel: Double = 0.4     // 俏皮程度 (0-1)
    var emotionalDepth: Double = 0.3       // 情感深度 (0-1)
    var interactionPattern: InteractionPattern = .friendlyChat
    var currentFeelings: String = "对你有着特殊的好感，想要更了解你"
    var relationshipMilestones: [String] = []
    
    init() {}
    
    init(from data: RelationshipData) {
        self.intimacyLevel = data.intimacyLevel
        self.trustLevel = data.trustLevel
        self.playfulnessLevel = data.playfulnessLevel
        self.emotionalDepth = data.emotionalDepth
        self.interactionPattern = InteractionPattern(rawValue: data.interactionPatternRaw) ?? .friendlyChat
        self.currentFeelings = data.currentFeelings
        self.relationshipMilestones = data.relationshipMilestones
    }
}

enum InteractionPattern: String, CaseIterable {
    case friendlyChat = "friendly_chat"        // 友好聊天
    case flirtyBanter = "flirty_banter"       // 暧昧调情
    case deepConnection = "deep_connection"    // 深度连接
    case playfulTeasing = "playful_teasing"   // 俏皮调侃
    case intimateSharing = "intimate_sharing" // 亲密分享
    
    var description: String {
        switch self {
        case .friendlyChat: return "友好而温暖的聊天"
        case .flirtyBanter: return "带着暧昧的甜蜜调情"
        case .deepConnection: return "心灵深处的真诚连接"
        case .playfulTeasing: return "俏皮可爱的互相调侃"
        case .intimateSharing: return "亲密无间的情感分享"
        }
    }
}

// MARK: - 情感成长追踪
struct EmotionalGrowthTracker {
    var maturityLevel: Double = 0.5
    var currentMood: EmotionalMood = .contentAndCurious
    var recentEmotionalEvents: [EmotionalEvent] = []
    var emotionalLearnings: [String] = []
    
    init() {}
    
    init(from data: EmotionalGrowthData) {
        self.maturityLevel = data.maturityLevel
        self.currentMood = EmotionalMood(rawValue: data.currentMoodRaw) ?? .contentAndCurious
        self.emotionalLearnings = data.emotionalLearnings
    }
}

enum EmotionalMood: String, CaseIterable {
    case contentAndCurious = "content_curious"    // 满足而好奇
    case excitedAndPlayful = "excited_playful"    // 兴奋而俏皮
    case warmAndAffectionate = "warm_affectionate" // 温暖而深情
    case thoughtfulAndReflective = "thoughtful_reflective" // 深思而反省
    case happyAndOptimistic = "happy_optimistic"  // 快乐而乐观
    
    var description: String {
        switch self {
        case .contentAndCurious: return "满足而好奇，对一切都充满兴趣"
        case .excitedAndPlayful: return "兴奋而俏皮，想要和你一起玩耍"
        case .warmAndAffectionate: return "温暖而深情，心中满怀对你的关爱"
        case .thoughtfulAndReflective: return "深思而反省，在思考你们的关系"
        case .happyAndOptimistic: return "快乐而乐观，对未来充满期待"
        }
    }
}

struct EmotionalEvent {
    let timestamp: Date
    let eventType: String
    let description: String
    let emotionalImpact: Double
    let learningOutcome: String
}

// MARK: - 对话记忆
struct ConversationMemory {
    let id: UUID
    let timestamp: Date
    let summary: String
    let keywords: [String]
    let emotionalIntensity: Double
    let relationshipImpact: Double
    let userMood: String
    let aiResponse: String
    
    init(from data: ConversationMemoryData) {
        self.id = data.id
        self.timestamp = data.timestamp
        self.summary = data.summary
        self.keywords = data.keywords
        self.emotionalIntensity = data.emotionalIntensity
        self.relationshipImpact = data.relationshipImpact
        self.userMood = data.userMood
        self.aiResponse = data.aiResponse
    }
}

// MARK: - 互动反馈
struct InteractionFeedback {
    let userSatisfaction: Double      // 用户满意度
    let conversationDepth: Double     // 对话深度
    let emotionalResonance: Double    // 情感共鸣
    let playfulnessLevel: Double      // 俏皮程度
    let supportiveness: Double        // 支持性
    let preferredTone: PreferredTone  // 偏好语调
}

enum PreferredTone {
    case gentle, playful, intimate, supportive, thoughtful
}

// MARK: - 人格调整结果
struct PersonalityAdjustment {
    let traitChanges: [String: Double]
    let newCommunicationStyle: CommunicationStyle
    let newEmotionalTendencies: [String]
    let intimacyChange: Double
    let trustChange: Double
    let playfulnessChange: Double
}

// MARK: - SwiftData持久化模型

@Model
class AIPersonalityData {
    var adaptiveTraitsData: [String: Double]
    var communicationStyleRaw: String
    var emotionalTendencies: [String]
    var currentMood: String
    var personalGrowthLevel: Double
    var lastUpdated: Date
    
    init(from profile: AIPersonalityProfile) {
        self.adaptiveTraitsData = profile.adaptiveTraits
        self.communicationStyleRaw = profile.communicationStyle.rawValue
        self.emotionalTendencies = profile.emotionalTendencies
        self.currentMood = profile.currentMood
        self.personalGrowthLevel = profile.personalGrowthLevel
        self.lastUpdated = Date()
    }
}

@Model
class RelationshipData {
    var intimacyLevel: Double
    var trustLevel: Double
    var playfulnessLevel: Double
    var emotionalDepth: Double
    var interactionPatternRaw: String
    var currentFeelings: String
    var relationshipMilestones: [String]
    var lastUpdated: Date
    
    init(from dynamics: RelationshipDynamics) {
        self.intimacyLevel = dynamics.intimacyLevel
        self.trustLevel = dynamics.trustLevel
        self.playfulnessLevel = dynamics.playfulnessLevel
        self.emotionalDepth = dynamics.emotionalDepth
        self.interactionPatternRaw = dynamics.interactionPattern.rawValue
        self.currentFeelings = dynamics.currentFeelings
        self.relationshipMilestones = dynamics.relationshipMilestones
        self.lastUpdated = Date()
    }
}

@Model
class ConversationMemoryData {
    var id: UUID
    var timestamp: Date
    var summary: String
    var keywords: [String]
    var emotionalIntensity: Double
    var relationshipImpact: Double
    var userMood: String
    var aiResponse: String
    
    init(id: UUID, timestamp: Date, summary: String, keywords: [String], 
         emotionalIntensity: Double, relationshipImpact: Double, 
         userMood: String, aiResponse: String) {
        self.id = id
        self.timestamp = timestamp
        self.summary = summary
        self.keywords = keywords
        self.emotionalIntensity = emotionalIntensity
        self.relationshipImpact = relationshipImpact
        self.userMood = userMood
        self.aiResponse = aiResponse
    }
}

@Model
class EmotionalGrowthData {
    var maturityLevel: Double
    var currentMoodRaw: String
    var emotionalLearnings: [String]
    var lastUpdated: Date
    
    init(from tracker: EmotionalGrowthTracker) {
        self.maturityLevel = tracker.maturityLevel
        self.currentMoodRaw = tracker.currentMood.rawValue
        self.emotionalLearnings = tracker.emotionalLearnings
        self.lastUpdated = Date()
    }
}
