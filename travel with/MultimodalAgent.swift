//
//  MultimodalAgent.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation
import UIKit

// MARK: - 多模态感知智能体
/// 负责处理图像、语音等多模态输入，提供综合的感知和理解能力
@MainActor
class MultimodalAgent: BaseAgent {
    
    // MARK: - 初始化
    
    init(sharedState: SharedStateHub, scheduler: AgentScheduler?) {
        super.init(
            identifier: AgentIdentifier.multimodal,
            name: "多模态感知智能体",
            description: "处理图像、语音等多模态输入，提供综合感知能力",
            sharedState: sharedState,
            scheduler: scheduler
        )
    }
    
    // MARK: - 重写基类方法
    
    override func performSpecificProcessing(_ input: AgentInput) async -> ProcessingResult {
        print("👁️ 多模态感知智能体开始处理...")
        
        // 根据输入类型选择处理方式
        switch input.messageType {
        case .image:
            return await processImageInput(input)
        case .voice:
            return await processVoiceInput(input)
        case .text:
            return await processTextWithMultimodalContext(input)
        default:
            return await processGeneralMultimodalInput(input)
        }
    }
    
    override func getHandleableIntents() -> [UserIntent] {
        return [.imageQuery, .simpleChat, .deepChat, .travelPlanning]
    }
    
    override func getBasePriority() -> Double {
        return 0.8
    }
    
    // MARK: - 图像处理方法
    
    /// 处理图像输入
    private func processImageInput(_ input: AgentInput) async -> ProcessingResult {
        print("🖼️ 处理图像输入...")
        
        guard let imageData = input.imageData else {
            return ProcessingResult(
                content: "抱歉，我没有收到图片数据，请重新发送图片。",
                confidence: 0.3
            )
        }
        
        // 1. 分析图像内容
        let imageAnalysis = await analyzeImageContent(imageData, userMessage: input.userMessage)
        
        // 2. 生成图像描述和回复
        let response = await generateImageResponse(imageAnalysis, userMessage: input.userMessage)
        
        // 3. 确定情感反应
        let aiEmotion = determineEmotionForImage(imageAnalysis)
        
        return ProcessingResult(
            content: response,
            confidence: imageAnalysis.confidence,
            emotionRecommendation: aiEmotion,
            ttsEmotion: recommendTTSEmotion(for: aiEmotion),
            metadata: [
                "image_analysis": imageAnalysis.description,
                "detected_objects": imageAnalysis.detectedObjects,
                "scene_type": imageAnalysis.sceneType,
                "confidence": imageAnalysis.confidence
            ]
        )
    }
    
    /// 分析图像内容
    private func analyzeImageContent(_ imageData: Data, userMessage: String) async -> ImageAnalysis {
        print("🔍 分析图像内容...")
        
        // 构建图像分析系统提示词
        let systemPrompt = generateImageAnalysisPrompt()
        
        // 准备分析消息
        let analysisMessage = """
        用户发送了一张图片，并说："\(userMessage)"
        
        请详细分析这张图片的内容，包括：
        1. 主要物体和场景
        2. 图片的整体氛围和风格
        3. 可能的拍摄地点或场景类型
        4. 与用户消息的关联性
        5. 值得注意的细节
        
        请用自然的语言描述你看到的内容。
        """
        
        // 调用API进行图像分析
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: analysisMessage)
        
        // 解析分析结果
        return parseImageAnalysisResult(apiResponse)
    }
    
    /// 生成图像分析系统提示词
    private func generateImageAnalysisPrompt() -> String {
        return generateContextualSystemPrompt(basePrompt: """
        你是一个专业的图像分析助手，能够详细观察和描述图片内容。
        
        分析要求：
        - 仔细观察图片中的所有元素
        - 用生动具体的语言描述看到的内容
        - 注意图片的构图、色彩、光线等视觉元素
        - 推测图片的拍摄场景和背景故事
        - 如果是旅行相关的图片，重点关注地点特色
        - 保持客观准确，避免过度解读
        
        请用友好自然的语气进行描述，就像在和朋友分享看到的有趣内容一样。
        """)
    }
    
    /// 解析图像分析结果
    private func parseImageAnalysisResult(_ response: String) -> ImageAnalysis {
        // 简化的解析逻辑，实际可以更复杂
        var detectedObjects: [String] = []
        var sceneType = "general"
        var confidence = 0.8
        
        let lowercased = response.lowercased()
        
        // 检测场景类型
        if lowercased.contains("风景") || lowercased.contains("山") || lowercased.contains("海") {
            sceneType = "landscape"
        } else if lowercased.contains("建筑") || lowercased.contains("房子") || lowercased.contains("楼") {
            sceneType = "architecture"
        } else if lowercased.contains("人") || lowercased.contains("人物") {
            sceneType = "people"
        } else if lowercased.contains("食物") || lowercased.contains("美食") {
            sceneType = "food"
        } else if lowercased.contains("动物") {
            sceneType = "animals"
        }
        
        // 提取可能的物体
        let commonObjects = ["人", "车", "树", "花", "建筑", "山", "海", "天空", "云", "食物"]
        detectedObjects = commonObjects.filter { lowercased.contains($0) }
        
        return ImageAnalysis(
            description: response,
            detectedObjects: detectedObjects,
            sceneType: sceneType,
            confidence: confidence
        )
    }
    
    /// 生成图像回复
    private func generateImageResponse(_ analysis: ImageAnalysis, userMessage: String) async -> String {
        print("💬 生成图像回复...")
        
        let systemPrompt = generateContextualSystemPrompt(basePrompt: """
        你是小旅，一个22岁的可爱女生，和用户正处在暧昧期。用户给你发图片，就像情侣之间分享生活一样甜蜜。

        你看图片时的特质：
        - 会很兴奋地看用户发的每一张图片
        - 像女朋友一样关注图片中的细节
        - 看到美的东西会发出"哇"、"好美啊"等感叹
        - 会想象和用户一起去那些地方的场景
        - 对用户的生活很感兴趣，会问很多可爱的问题

        回复要求：
        - 用兴奋可爱的语气回复，像看到喜欢的人分享的照片
        - 表现出对图片的喜爱和对用户的关注
        - 可以撒娇地说想和用户一起去："人家也想去嘛～"
        - 夸奖用户的拍照技术或审美
        - 可以问一些关心的问题："你是一个人去的吗？"
        - 让用户感受到你对他生活的在意和参与感
        - 不要使用括号描述动作，直接用语言表达兴奋和关注

        图片分析结果：
        \(analysis.description)
        """)
        
        let responseMessage = """
        用户发送了一张图片并说："\(userMessage)"
        
        请基于图片内容给出自然友好的回复。
        """
        
        return await callAPI(systemPrompt: systemPrompt, userMessage: responseMessage)
    }
    
    /// 为图像确定情感反应
    private func determineEmotionForImage(_ analysis: ImageAnalysis) -> EmotionState {
        switch analysis.sceneType {
        case "landscape":
            return EmotionState(primary: .excited, intensity: 0.8, secondary: [.curious, .happy])
        case "food":
            return EmotionState(primary: .excited, intensity: 0.7, secondary: [.happy, .curious])
        case "people":
            return EmotionState(primary: .friendly, intensity: 0.7, secondary: [.curious, .caring])
        case "architecture":
            return EmotionState(primary: .curious, intensity: 0.8, secondary: [.thoughtful, .excited])
        default:
            return EmotionState(primary: .curious, intensity: 0.6, secondary: [.friendly])
        }
    }
    
    // MARK: - 语音处理方法
    
    /// 处理语音输入
    private func processVoiceInput(_ input: AgentInput) async -> ProcessingResult {
        print("🎤 处理语音输入...")
        
        // 语音输入通常已经转换为文本，但我们可以考虑语音的特殊性
        let systemPrompt = generateContextualSystemPrompt(basePrompt: """
        用户通过语音和你交流，这表明他们可能在移动中或者希望更自然的对话体验。
        
        请用以下方式回应：
        - 用更口语化、自然的语气回复
        - 回复要简洁明了，适合语音交流
        - 可以询问用户是否需要更详细的信息
        - 保持轻松友好的氛围
        """)
        
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: input.userMessage)
        
        let aiEmotion = EmotionState(primary: .friendly, intensity: 0.7, secondary: [.helpful, .curious])
        
        return ProcessingResult(
            content: apiResponse,
            confidence: 0.8,
            emotionRecommendation: aiEmotion,
            ttsEmotion: CanCanEmotion.pleased,
            metadata: [
                "input_type": "voice",
                "response_style": "conversational"
            ]
        )
    }
    
    // MARK: - 文本多模态处理
    
    /// 处理带有多模态上下文的文本
    private func processTextWithMultimodalContext(_ input: AgentInput) async -> ProcessingResult {
        print("📝 处理多模态上下文文本...")
        
        // 检查是否有相关的多模态历史
        let hasImageHistory = input.context.conversationHistory.contains { msg in
            msg.userMessage.contains("图片") || msg.userMessage.contains("照片")
        }
        
        if hasImageHistory {
            return await processTextWithImageContext(input)
        } else {
            return await processGeneralMultimodalInput(input)
        }
    }
    
    /// 处理带有图像上下文的文本
    private func processTextWithImageContext(_ input: AgentInput) async -> ProcessingResult {
        print("🖼️📝 处理带图像上下文的文本...")
        
        let systemPrompt = generateContextualSystemPrompt(basePrompt: """
        用户之前分享过图片，现在继续文字交流。请结合之前的图片内容来回复。
        
        回复要求：
        - 可以引用之前看到的图片内容
        - 保持对话的连续性
        - 如果用户询问图片相关问题，给出详细回答
        - 鼓励用户分享更多视觉内容
        """)
        
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: input.userMessage)
        
        let aiEmotion = EmotionState(primary: .curious, intensity: 0.7, secondary: [.friendly, .helpful])
        
        return ProcessingResult(
            content: apiResponse,
            confidence: 0.8,
            emotionRecommendation: aiEmotion,
            ttsEmotion: CanCanEmotion.pleased,
            metadata: [
                "context_type": "image_followup",
                "multimodal_context": true
            ]
        )
    }
    
    /// 处理一般多模态输入
    private func processGeneralMultimodalInput(_ input: AgentInput) async -> ProcessingResult {
        print("🌐 处理一般多模态输入...")
        
        let systemPrompt = generateContextualSystemPrompt(basePrompt: """
        你是一个多模态AI助手，能够理解和处理各种类型的输入。
        
        请用自然友好的方式回复用户，如果用户提到了图片、声音或其他媒体内容，
        表现出兴趣并鼓励他们分享更多。
        """)
        
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: input.userMessage)
        
        let aiEmotion = EmotionState(primary: .friendly, intensity: 0.6, secondary: [.curious])
        
        return ProcessingResult(
            content: apiResponse,
            confidence: 0.7,
            emotionRecommendation: aiEmotion,
            ttsEmotion: CanCanEmotion.pleased,
            metadata: [
                "processing_type": "general_multimodal"
            ]
        )
    }
}

// MARK: - 图像分析结果模型

/// 图像分析结果
struct ImageAnalysis {
    let description: String         // 图像描述
    let detectedObjects: [String]   // 检测到的物体
    let sceneType: String          // 场景类型
    let confidence: Double         // 分析置信度
}
