//
//  SharedLocationManager.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import Foundation
import CoreLocation
import SwiftUI

// MARK: - 共享位置管理器
class SharedLocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
    static let shared = SharedLocationManager()
    
    private let locationManager = CLLocationManager()
    
    @Published var userLocation: CLLocation?
    @Published var authorizationStatus: CLAuthorizationStatus = .notDetermined
    @Published var deviceHeading: CLLocationDirection = 0
    @Published var userCourse: CLLocationDirection = 0
    @Published var isLocationUpdating = false
    @Published var isHeadingUpdating = false
    
    private override init() {
        super.init()
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.headingFilter = 1.0 // 1度变化才更新
    }
    
    // MARK: - 权限管理
    
    func requestPermission() {
        print("🗺️ 请求位置权限...")
        locationManager.requestWhenInUseAuthorization()
    }
    
    // MARK: - 位置更新
    
    func startLocationUpdates() {
        guard authorizationStatus == .authorizedWhenInUse || authorizationStatus == .authorizedAlways else {
            print("❌ 位置权限未授权: \(authorizationStatus)")
            requestPermission()
            return
        }
        
        print("📍 开始位置更新...")
        locationManager.startUpdatingLocation()
        isLocationUpdating = true
    }
    
    func stopLocationUpdates() {
        print("📍 停止位置更新...")
        locationManager.stopUpdatingLocation()
        isLocationUpdating = false
    }
    
    // MARK: - 方向更新
    
    func startUpdatingHeading() {
        guard CLLocationManager.headingAvailable() else {
            print("❌ 设备不支持方向传感器")
            return
        }
        
        print("🧭 开始方向更新...")
        locationManager.startUpdatingHeading()
        isHeadingUpdating = true
    }
    
    func stopUpdatingHeading() {
        print("🧭 停止方向更新...")
        locationManager.stopUpdatingHeading()
        isHeadingUpdating = false
    }
    
    // MARK: - 便捷方法
    
    func startAllUpdates() {
        startLocationUpdates()
        startUpdatingHeading()
    }
    
    func stopAllUpdates() {
        stopLocationUpdates()
        stopUpdatingHeading()
    }
    
    // MARK: - CLLocationManagerDelegate
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        DispatchQueue.main.async {
            self.userLocation = location
            self.userCourse = location.course >= 0 ? location.course : 0
        }
        
        print("📍 位置更新: \(location.coordinate.latitude), \(location.coordinate.longitude)")
    }
    
    func locationManager(_ manager: CLLocationManager, didUpdateHeading newHeading: CLHeading) {
        guard newHeading.headingAccuracy >= 0 else { return }
        
        let heading = newHeading.trueHeading >= 0 ? newHeading.trueHeading : newHeading.magneticHeading
        
        DispatchQueue.main.async {
            self.deviceHeading = heading
        }
        
        print("🧭 方向更新: \(heading)°")
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        DispatchQueue.main.async {
            self.authorizationStatus = status
        }
        
        print("🔐 位置权限状态变更: \(status)")
        
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            print("✅ 位置权限已授权")
        case .denied, .restricted:
            print("❌ 位置权限被拒绝")
        case .notDetermined:
            print("⏳ 位置权限未确定")
        @unknown default:
            print("❓ 未知的位置权限状态")
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("❌ 位置服务错误: \(error.localizedDescription)")
    }
}

// MARK: - 便捷扩展
extension SharedLocationManager {
    var userCoordinate: CLLocationCoordinate2D? {
        return userLocation?.coordinate
    }
    
    var isAuthorized: Bool {
        return authorizationStatus == .authorizedWhenInUse || authorizationStatus == .authorizedAlways
    }
    
    var authorizationStatusText: String {
        switch authorizationStatus {
        case .notDetermined:
            return "未确定"
        case .restricted:
            return "受限制"
        case .denied:
            return "已拒绝"
        case .authorizedAlways:
            return "始终允许"
        case .authorizedWhenInUse:
            return "使用时允许"
        @unknown default:
            return "未知"
        }
    }
}
