//
//  PlacesView.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI
import MapKit

struct PlacesView: View {
    @StateObject private var placeManager = PlaceManager()
    @State private var searchText = ""
    @State private var searchResults: [MKMapItem] = []
    @State private var isSearching = false
    @State private var showingAddPlanAlert = false
    @State private var newPlanName = ""
    @State private var selectedPlace: Place?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                PlaceSearchBarView(
                    searchText: $searchText,
                    searchResults: $searchResults,
                    isSearching: $isSearching,
                    onAddPlace: { mapItem in
                        addPlaceFromMapItem(mapItem)
                    }
                )
                .padding(.horizontal)
                .padding(.top, 8)
                
                // 计划导航栏
                PlanNavigationView(
                    placeManager: placeManager,
                    showingAddPlanAlert: $showingAddPlanAlert
                )
                .padding(.horizontal)
                .padding(.vertical, 8)
                
                // 地点卡片网格列表
                ScrollView {
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 8),
                        GridItem(.flexible(), spacing: 8)
                    ], spacing: 12) {
                        let places = placeManager.getPlaces()

                        if places.isEmpty {
                            // 空状态占据两列
                            EmptyPlacesView()
                                .gridCellColumns(2)
                                .padding(.top, 50)
                        } else {
                            ForEach(places, id: \.id) { place in
                                PlaceCardView(
                                    place: place,
                                    onTap: {
                                        selectedPlace = place
                                    },
                                    onDelete: {
                                        placeManager.removePlace(place)
                                    }
                                )
                            }
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top, 8)
                }
            }
            .navigationTitle("地点")
            .navigationBarTitleDisplayMode(.large)
            .background(Color(.systemGroupedBackground))
        }
        .sheet(item: $selectedPlace) { place in
            PlaceDetailView(place: place)
        }
        .alert("新建计划", isPresented: $showingAddPlanAlert) {
            TextField("计划名称", text: $newPlanName)
            Button("取消", role: .cancel) {
                newPlanName = ""
            }
            Button("创建") {
                if !newPlanName.isEmpty {
                    placeManager.createTravelPlan(name: newPlanName)
                    newPlanName = ""
                }
            }
        } message: {
            Text("请输入新旅行计划的名称")
        }
        .onAppear {
            placeManager.loadTravelPlans()
        }
    }
    
    private func addPlaceFromMapItem(_ mapItem: MKMapItem) {
        let place = Place(
            name: mapItem.name ?? "未知地点",
            address: mapItem.placemark.title ?? "",
            coordinate: mapItem.placemark.coordinate,
            coverImageURL: generateCoverImageURL(for: mapItem)
        )
        
        placeManager.addPlace(place)
        
        // 清空搜索
        searchText = ""
        searchResults = []
    }
    
    private func generateCoverImageURL(for mapItem: MKMapItem) -> String {
        // 使用Unsplash API生成相关的封面图片
        let query = mapItem.name?.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "travel"
        return "https://source.unsplash.com/400x300/?travel,\(query)"
    }
}

// MARK: - 地点搜索栏视图
struct PlaceSearchBarView: View {
    @Binding var searchText: String
    @Binding var searchResults: [MKMapItem]
    @Binding var isSearching: Bool
    let onAddPlace: (MKMapItem) -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 搜索输入框
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("搜索地点...", text: $searchText)
                    .textFieldStyle(.plain)
                    .onSubmit {
                        if !searchText.isEmpty {
                            searchPlaces()
                        }
                    }
                
                if isSearching {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            
            // 搜索结果列表
            if !searchResults.isEmpty {
                VStack(spacing: 0) {
                    ForEach(searchResults.indices, id: \.self) { index in
                        let mapItem = searchResults[index]
                        
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(mapItem.name ?? "未知地点")
                                    .font(.body)
                                    .fontWeight(.medium)
                                
                                if let address = mapItem.placemark.title {
                                    Text(address)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .lineLimit(2)
                                }
                            }
                            
                            Spacer()
                            
                            Button(action: {
                                onAddPlace(mapItem)
                            }) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.title2)
                                    .foregroundColor(.blue)
                            }
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        
                        if index < searchResults.count - 1 {
                            Divider()
                                .padding(.leading, 12)
                        }
                    }
                }
                .background(Color(.systemBackground))
                .cornerRadius(10)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                .padding(.top, 4)
            }
        }
        .onChange(of: searchText) { _, newValue in
            if newValue.isEmpty {
                searchResults = []
                isSearching = false
            } else if newValue.count >= 2 {
                // 延迟搜索，避免频繁请求
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    if searchText == newValue && !newValue.isEmpty {
                        searchPlaces()
                    }
                }
            }
        }
    }
    
    private func searchPlaces() {
        guard !searchText.isEmpty else { return }
        
        isSearching = true
        
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = searchText
        request.resultTypes = [.pointOfInterest, .address]
        
        let search = MKLocalSearch(request: request)
        search.start { response, error in
            DispatchQueue.main.async {
                isSearching = false
                
                if let response = response {
                    searchResults = Array(response.mapItems.prefix(5)) // 限制显示5个结果
                } else if let error = error {
                    print("❌ 搜索失败: \(error.localizedDescription)")
                    searchResults = []
                }
            }
        }
    }
}

// MARK: - 计划导航视图
struct PlanNavigationView: View {
    @ObservedObject var placeManager: PlaceManager
    @Binding var showingAddPlanAlert: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("计划")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: {
                    showingAddPlanAlert = true
                }) {
                    Image(systemName: "plus.circle")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(placeManager.travelPlans, id: \.id) { plan in
                        PlanTabView(
                            plan: plan,
                            isSelected: placeManager.selectedPlan?.id == plan.id,
                            onTap: {
                                placeManager.selectedPlan = plan
                            },
                            placeManager: placeManager
                        )
                    }
                }
                .padding(.horizontal, 4)
            }
        }
    }
}

// MARK: - 计划标签视图
struct PlanTabView: View {
    let plan: TravelPlan
    let isSelected: Bool
    let onTap: () -> Void
    @ObservedObject var placeManager: PlaceManager

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 6) {
                // 为系统计划添加图标
                if plan.name == "全部地点" {
                    Image(systemName: "globe")
                        .font(.caption)
                } else if plan.name == "默认计划" {
                    Image(systemName: "star.fill")
                        .font(.caption)
                }

                Text(plan.name)
                    .font(.subheadline)
                    .fontWeight(isSelected ? .semibold : .medium)

                Text("\(getPlaceCount())")
                    .font(.caption)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(isSelected ? Color.white.opacity(0.3) : Color.secondary.opacity(0.2))
                    .cornerRadius(8)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(getBackgroundColor())
            .cornerRadius(20)
        }
        .buttonStyle(.plain)
    }

    private func getPlaceCount() -> Int {
        if plan.name == "全部地点" {
            return placeManager.getAllPlaces().count
        } else {
            return plan.places.count
        }
    }

    private func getBackgroundColor() -> Color {
        if isSelected {
            if plan.name == "全部地点" {
                return Color.blue
            } else if plan.name == "默认计划" {
                return Color.orange
            } else {
                return Color.blue
            }
        } else {
            return Color(.systemGray6)
        }
    }
}

// MARK: - 地点卡片视图
struct PlaceCardView: View {
    let place: Place
    let onTap: () -> Void
    let onDelete: () -> Void

    @State private var offset: CGFloat = 0
    @State private var isShowingDelete = false
    @State private var isDragging = false

    private let deleteButtonWidth: CGFloat = 80

    var body: some View {
        ZStack(alignment: .trailing) {
            // 删除按钮背景层
            deleteButton
                .opacity(isShowingDelete ? 1 : 0)

            // 主卡片内容
            cardContent
                .offset(x: offset)
                .gesture(swipeGesture)
        }
        .clipped()
        // 拖拽时禁用动画，确保完全跟随手指；非拖拽时启用动画
        .animation(isDragging ? nil : .spring(response: 0.25, dampingFraction: 0.7), value: offset)
        .animation(.spring(response: 0.25, dampingFraction: 0.7), value: isShowingDelete)
    }

    private var cardContent: some View {

        Button(action: {
            if !isShowingDelete {
                onTap()
            } else {
                // 收回删除按钮
                withAnimation {
                    offset = 0
                    isShowingDelete = false
                }
            }
        }) {
            VStack(alignment: .leading, spacing: 0) {
                // 封面图片
                AsyncImage(url: URL(string: place.coverImageURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .overlay(
                            Image(systemName: "photo")
                                .font(.title2)
                                .foregroundColor(.secondary)
                        )
                }
                .frame(height: 100)
                .clipped()

                // 地点信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(place.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    Text(place.address)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(2)

                    HStack {
                        Image(systemName: "location.fill")
                            .font(.caption2)
                            .foregroundColor(.blue)

                        Text("详情")
                            .font(.caption2)
                            .foregroundColor(.blue)

                        Spacer()

                        Text(formatDate(place.createdDate))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(8)
            }
        }
        .buttonStyle(.plain)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }

    private var deleteButton: some View {
        HStack(spacing: 0) {
            Spacer()
            Button(action: {
                withAnimation {
                    onDelete()
                    offset = 0
                    isShowingDelete = false
                }
            }) {
                VStack(spacing: 4) {
                    Image(systemName: "trash.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                    Text("删除")
                        .font(.caption)
                        .foregroundColor(.white)
                }
                .frame(width: deleteButtonWidth)
                .frame(maxHeight: .infinity)
                .background(Color.red)
                .cornerRadius(12, corners: [.topRight, .bottomRight])
            }
        }
    }

    private var swipeGesture: some Gesture {
        DragGesture(minimumDistance: 0)
            .onChanged { value in
                // 标记正在拖拽
                if !isDragging {
                    isDragging = true
                }

                let translation = value.translation.width

                // 完全跟随手指移动，实时响应
                if translation < 0 {
                    // 向左滑动，限制最大偏移为删除按钮宽度
                    offset = max(translation, -deleteButtonWidth)
                } else if translation > 0 {
                    // 向右滑动处理
                    if offset < 0 {
                        // 如果已经有负偏移，允许滑回
                        offset = min(offset + translation, 0)
                    } else {
                        // 如果没有偏移，允许轻微的右滑但不超过10点
                        offset = min(translation, 10)
                    }
                }

                // 实时更新删除按钮显示状态
                isShowingDelete = offset < -5
            }
            .onEnded { value in
                // 标记拖拽结束
                isDragging = false

                let translation = value.translation.width
                let velocity = value.velocity.width

                // 使用动画回到最终位置
                withAnimation(.spring(response: 0.25, dampingFraction: 0.7)) {
                    // 考虑滑动速度和距离的智能判断
                    let shouldShowDelete = translation < -15 ||
                                         (translation < -5 && velocity < -200) ||
                                         offset < -deleteButtonWidth * 0.3

                    if shouldShowDelete {
                        // 显示删除按钮
                        offset = -deleteButtonWidth
                        isShowingDelete = true
                    } else {
                        // 回弹到原位
                        offset = 0
                        isShowingDelete = false
                    }
                }
            }
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - 圆角扩展
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - 空状态视图
struct EmptyPlacesView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "location.circle")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            Text("还没有添加地点")
                .font(.title3)
                .fontWeight(.medium)
                .foregroundColor(.primary)

            Text("搜索并添加您想去的地方")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
}

// MARK: - 地点详情视图
struct PlaceDetailView: View {
    let place: Place
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 封面图片
                    AsyncImage(url: URL(string: place.coverImageURL ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Rectangle()
                            .fill(Color(.systemGray5))
                            .overlay(
                                Image(systemName: "photo")
                                    .font(.largeTitle)
                                    .foregroundColor(.secondary)
                            )
                    }
                    .frame(height: 200)
                    .clipped()
                    .cornerRadius(12)

                    VStack(alignment: .leading, spacing: 12) {
                        // 地点名称
                        Text(place.name)
                            .font(.largeTitle)
                            .fontWeight(.bold)

                        // 地址信息
                        HStack(alignment: .top, spacing: 8) {
                            Image(systemName: "location.fill")
                                .foregroundColor(.blue)
                                .font(.body)

                            Text(place.address)
                                .font(.body)
                                .foregroundColor(.secondary)
                        }

                        // 坐标信息
                        HStack(spacing: 8) {
                            Image(systemName: "globe")
                                .foregroundColor(.green)
                                .font(.body)

                            Text("坐标: \(String(format: "%.4f", place.latitude)), \(String(format: "%.4f", place.longitude))")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }

                        // 添加时间
                        HStack(spacing: 8) {
                            Image(systemName: "calendar")
                                .foregroundColor(.orange)
                                .font(.body)

                            Text("添加于: \(formatDate(place.createdDate))")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }

                        Divider()

                        // 待完善提示
                        VStack(alignment: .leading, spacing: 8) {
                            Text("详细信息")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Text("地点详细内容待完善...")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .italic()
                        }
                    }
                    .padding(.horizontal)
                }
            }
            .navigationTitle("地点详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - 预览
#Preview {
    PlacesView()
}
