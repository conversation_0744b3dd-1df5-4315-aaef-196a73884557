//
//  AIService.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import Foundation
import UIKit
import SwiftUI
import SwiftData
import AVFoundation

// MARK: - AI角色设定数据模型
@Model
class AIRoleSettings {
    @Attribute(.unique) var id: UUID
    var systemPrompt: String
    var createdDate: Date
    var lastModified: Date

    init(systemPrompt: String) {
        self.id = UUID()
        self.systemPrompt = systemPrompt
        self.createdDate = Date()
        self.lastModified = Date()
    }

    // 默认系统提示词
    static let defaultPrompt = "你是我的好朋友，我们经常一起聊天。用朋友之间日常聊天的语气回复，要简短自然，就像微信聊天一样。不要太正式或者太长，偶尔可以用点表情符号。"
}

// MARK: - 消息模型
struct ChatMessage: Identifiable, Codable {
    var id: UUID
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let messageType: MessageType
    let imageData: Data?

    enum MessageType: String, Codable {
        case text = "text"
        case image = "image"
        case emoji = "emoji"
        case system = "system"
    }

    init(content: String, isFromUser: Bool, messageType: MessageType = .text, imageData: Data? = nil) {
        self.id = UUID()
        self.content = content
        self.isFromUser = isFromUser
        self.messageType = messageType
        self.timestamp = Date()
        self.imageData = imageData
    }

    // 用于从历史记录创建消息的初始化方法
    init(id: UUID, content: String, isFromUser: Bool, messageType: MessageType = .text, timestamp: Date = Date(), imageData: Data? = nil) {
        self.id = id
        self.content = content
        self.isFromUser = isFromUser
        self.messageType = messageType
        self.timestamp = timestamp
        self.imageData = imageData
    }
}

// MARK: - API响应模型
struct APIResponse: Codable {
    let choices: [Choice]
    
    struct Choice: Codable {
        let message: Message
        
        struct Message: Codable {
            let content: String
            let role: String
        }
    }
}

// MARK: - AI服务管理器
@MainActor
class AIService: ObservableObject {
    // 内置豆包API配置
    private let apiKey = "aeaa5a81-6333-4a78-83d2-418230bbf85f"
    private let baseURL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    private let rolePlayingModel = "ep-20250730103733-kzl7l"  // AI朋友聊天
    private let multimodalModel = "ep-20250730105426-fwzz6"   // 图像识别+助手
    private let imageRecognitionModel = "ep-20250730213933-4s55h"  // 专用图像识别模型
    
    @Published var messages: [ChatMessage] = []
    @Published var isLoading = false
    @Published var connectionStatus: ConnectionStatus = .disconnected

    // 历史记录管理器
    var historyManager: ChatHistoryManager?

    // 多智能体集成服务
    private var multiAgentService: MultiAgentIntegrationService?

    // ASR语音识别服务
    @Published var asrService: ASRService?

    // AI角色设定
    @Published var currentSystemPrompt: String = AIRoleSettings.defaultPrompt
    private var roleSettingsContext: ModelContext?
    
    enum ConnectionStatus: Equatable {
        case connected
        case disconnected
        case connecting
        case error(String)

        static func == (lhs: ConnectionStatus, rhs: ConnectionStatus) -> Bool {
            switch (lhs, rhs) {
            case (.connected, .connected),
                 (.disconnected, .disconnected),
                 (.connecting, .connecting):
                return true
            case (.error(let lhsMessage), .error(let rhsMessage)):
                return lhsMessage == rhsMessage
            default:
                return false
            }
        }
    }
    
    private let urlSession: URLSession
    
    init() {
        // 配置URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 60  // 增加到60秒
        config.timeoutIntervalForResource = 120 // 增加到120秒
        self.urlSession = URLSession(configuration: config)

        // 初始化历史记录管理器
        self.historyManager = ChatHistoryManager()

        // 初始化ASR服务
        self.asrService = ASRService()
        setupASRService()

        // 初始化AI角色设定数据库
        setupRoleSettingsDatabase()

        // 初始化多智能体集成服务
        self.multiAgentService = MultiAgentIntegrationService(legacyAIService: self)

        // 应用启动时在后台自动连接
        Task {
            await backgroundConnect()
            await loadRecentHistory()
            await loadRoleSettings()
        }
    }
    
    // MARK: - 后台连接
    private func backgroundConnect() async {
        connectionStatus = .connecting
        print("🔄 后台连接AI服务...")

        // 静默测试连接，不显示问候消息
        let testResponse = await withTimeout(seconds: 10) {
            await self.sendMessage("测试连接", useRolePlayingModel: true)
        } ?? "连接超时"

        if !testResponse.isEmpty && !testResponse.contains("请求失败") && !testResponse.contains("错误") && !testResponse.contains("URL错误") && !testResponse.contains("连接超时") {
            connectionStatus = .connected
            print("✅ AI后台连接成功")
        } else {
            connectionStatus = .connected // 仍然设置为已连接，使用离线模式
            print("⚠️ AI连接失败，使用离线模式: \(testResponse)")
        }
    }

    // MARK: - 连接并问候
    func connectAndGreet() async {
        connectionStatus = .connecting
        print("🔄 开始连接AI服务...")

        // 添加超时机制
        let greeting = await withTimeout(seconds: 10) {
            await self.sendMessage("嗨！今天想去哪玩呀？", useRolePlayingModel: true)
        } ?? "连接超时"

        print("📝 AI响应: \(greeting)")

        // 无论API是否成功，都设置为已连接状态，确保用户可以继续使用应用
        connectionStatus = .connected

        if !greeting.isEmpty && !greeting.contains("请求失败") && !greeting.contains("错误") && !greeting.contains("URL错误") && !greeting.contains("连接超时") {
            print("✅ AI连接成功，使用真实响应")
            // 添加AI问候消息
            let greetingMessage = ChatMessage(
                content: greeting,
                isFromUser: false,
                messageType: .text
            )
            messages.append(greetingMessage)
        } else {
            print("⚠️ API调用失败，使用默认问候: \(greeting)")
            // 如果API调用失败，显示默认问候
            let defaultGreeting = ChatMessage(
                content: "嗨！今天想去哪玩呀？😊\n\n(网络有点问题，不过咱们还是可以聊天的)",
                isFromUser: false,
                messageType: .text
            )
            messages.append(defaultGreeting)
        }

        print("🎉 连接流程完成，状态: \(connectionStatus)")
    }

    // MARK: - 超时辅助函数
    private func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async -> T) async -> T? {
        return await withTaskGroup(of: T?.self) { group in
            group.addTask {
                await operation()
            }

            group.addTask {
                try? await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
                return nil
            }

            let result = await group.next()
            group.cancelAll()
            return result ?? nil
        }
    }
    
    // MARK: - 发送文本消息
    func sendTextMessage(_ text: String) async {
        // 添加用户消息
        let userMessage = ChatMessage(content: text, isFromUser: true, messageType: .text)
        messages.append(userMessage)

        isLoading = true

        // 使用多智能体系统处理消息
        if let multiAgentService = multiAgentService {
            let response = await multiAgentService.processTextMessage(text)

            // 创建AI回复消息
            let aiMessage = ChatMessage(content: response.content, isFromUser: false, messageType: .text)
            messages.append(aiMessage)

            // 保存到历史记录
            await historyManager?.saveMessage(userMessage)
            await historyManager?.saveMessage(aiMessage)

            // 如果有推荐的TTS情感音色，应用它
            if let ttsEmotion = response.ttsEmotion {
                // 这里可以设置TTS情感音色
                print("🎵 推荐TTS音色: \(ttsEmotion.description)")
            }

        } else {
            // 回退到原有逻辑
            await sendTextMessageLegacy(text)
        }

        isLoading = false
    }

    // MARK: - 原有文本消息处理逻辑（作为备用）
    private func sendTextMessageLegacy(_ text: String) async {
        // 获取RAG上下文
        let ragContext = await historyManager?.getRAGContext() ?? ""

        // 构建带有历史上下文的消息
        let messageWithContext = ragContext.isEmpty ? text : """
        最近的对话历史：
        \(ragContext)

        当前消息：\(text)
        """

        // 发送到AI并获取回复
        let aiResponse = await sendMessage(messageWithContext, useRolePlayingModel: true)

        if !aiResponse.isEmpty {
            let aiMessage = ChatMessage(content: aiResponse, isFromUser: false, messageType: .text)
            messages.append(aiMessage)

            // 保存到历史记录
            await historyManager?.saveMessage(ChatMessage(content: text, isFromUser: true, messageType: .text))
            await historyManager?.saveMessage(aiMessage)
        }
    }
    
    // MARK: - 发送图像消息
    func sendImageMessage(_ image: UIImage, withText text: String = "") async {
        // 压缩图像
        guard let imageData = image.jpegData(compressionQuality: 0.7) else {
            return
        }

        // 添加用户消息（只显示图像，不显示文本）
        let userMessage = ChatMessage(
            content: text, // 空文本，只显示图像
            isFromUser: true,
            messageType: .image,
            imageData: imageData
        )
        messages.append(userMessage)

        isLoading = true

        // 使用多智能体系统处理图像消息
        if let multiAgentService = multiAgentService {
            let response = await multiAgentService.processImageMessage(image, withText: text)

            // 创建AI回复消息
            let aiMessage = ChatMessage(content: response.content, isFromUser: false, messageType: .text)
            messages.append(aiMessage)

            // 保存到历史记录
            await historyManager?.saveMessage(userMessage, imageDescription: response.content)
            await historyManager?.saveMessage(aiMessage)

            // 如果有推荐的TTS情感音色，应用它
            if let ttsEmotion = response.ttsEmotion {
                print("🎵 推荐TTS音色: \(ttsEmotion.description)")
            }

        } else {
            // 回退到原有逻辑
            await sendImageMessageLegacy(image, withText: text)
        }

        isLoading = false
    }

    // MARK: - 原有图像消息处理逻辑（作为备用）
    private func sendImageMessageLegacy(_ image: UIImage, withText text: String = "") async {
        guard let imageData = image.jpegData(compressionQuality: 0.7) else { return }

        // 转换为base64
        let base64Image = imageData.base64EncodedString()
        let imageURL = "data:image/jpeg;base64,\(base64Image)"

        // 使用新的双模型图像识别流程
        let result = await processImageWithDualModel(imageURL: imageURL, userText: text)

        if !result.chatResponse.isEmpty {
            let aiMessage = ChatMessage(content: result.chatResponse, isFromUser: false, messageType: .text)
            messages.append(aiMessage)

            // 保存到历史记录（包含图像描述）
            if let historyManager = self.historyManager {
                let userMessage = ChatMessage(content: text, isFromUser: true, messageType: .image, imageData: imageData)
                await historyManager.saveMessage(userMessage, imageDescription: result.imageDescription)
                await historyManager.saveMessage(aiMessage)
            }
        }
    }
    
    // MARK: - 发送消息到API (文本)
    func sendMessage(_ message: String, useRolePlayingModel: Bool) async -> String {
        let model = useRolePlayingModel ? rolePlayingModel : multimodalModel

        let systemPrompt = useRolePlayingModel ?
            currentSystemPrompt :
            "你是一个专业的旅行助手，提供实用的旅行建议。"

        let requestBody: [String: Any] = [
            "model": model,
            "messages": [
                ["role": "system", "content": systemPrompt],
                ["role": "user", "content": message]
            ]
        ]

        return await makeAPIRequest(requestBody: requestBody)
    }
    
    // MARK: - 双模型图像处理流程
    private func processImageWithDualModel(imageURL: String, userText: String) async -> (chatResponse: String, imageDescription: String) {
        // 第一步：使用专用图像识别模型识别图片内容
        let recognitionPrompt = userText.isEmpty ? "图片主要讲了什么?" : userText
        let imageRecognitionResult = await sendImageToRecognitionAPI(imageURL: imageURL, text: recognitionPrompt)

        if imageRecognitionResult.isEmpty || imageRecognitionResult.contains("请求失败") || imageRecognitionResult.contains("错误") {
            // 如果图像识别失败，回退到原有的多模态模型
            let fallbackResponse = await sendImageToAPI(imageURL: imageURL, text: userText)
            return (fallbackResponse, "[图像]:一张图片")
        }

        // 生成简短的图像描述用于历史记录
        let shortDescription = await generateShortImageDescription(imageRecognitionResult)

        // 第二步：将识别结果传递给角色扮演模型进行友好回复
        let chatPrompt = """
        我看到了这张图片的内容：\(imageRecognitionResult)

        你是我的好朋友，我们经常一起聊天。请用朋友之间日常聊天的语气回应这张图片，要简短自然，就像微信聊天一样。不要太正式或者太长，偶尔可以用点表情符号。如果是旅行相关的可以简单说说想法，但别像导游一样介绍。
        """

        let chatResponse = await sendMessage(chatPrompt, useRolePlayingModel: true)
        return (chatResponse, shortDescription)
    }

    // MARK: - 生成简短图像描述
    private func generateShortImageDescription(_ detailedDescription: String) async -> String {
        let prompt = """
        请将以下详细的图像描述简化为一句话，格式为"[图像]:一个什么图像"，要简洁明了：

        \(detailedDescription)

        只返回简化后的描述，不要其他内容。
        """

        let shortDesc = await sendMessage(prompt, useRolePlayingModel: true)

        // 确保格式正确
        if shortDesc.hasPrefix("[图像]:") {
            return shortDesc
        } else {
            return "[图像]:\(shortDesc)"
        }
    }

    // MARK: - 专用图像识别API
    private func sendImageToRecognitionAPI(imageURL: String, text: String) async -> String {
        let requestBody: [String: Any] = [
            "model": imageRecognitionModel,
            "messages": [
                [
                    "role": "user",
                    "content": [
                        ["type": "text", "text": text],
                        ["type": "image_url", "image_url": ["url": imageURL]]
                    ]
                ]
            ]
        ]

        return await makeAPIRequest(requestBody: requestBody)
    }

    // MARK: - 发送图像到API (备用多模态模型)
    private func sendImageToAPI(imageURL: String, text: String) async -> String {
        let requestBody: [String: Any] = [
            "model": multimodalModel,
            "messages": [
                [
                    "role": "user",
                    "content": [
                        ["type": "text", "text": text],
                        ["type": "image_url", "image_url": ["url": imageURL]]
                    ]
                ]
            ]
        ]

        return await makeAPIRequest(requestBody: requestBody)
    }
    
    // MARK: - 通用API请求
    private func makeAPIRequest(requestBody: [String: Any]) async -> String {
        guard let url = URL(string: baseURL) else {
            print("❌ URL解析失败: \(baseURL)")
            return "URL错误"
        }

        print("🌐 发送API请求到: \(baseURL)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            request.httpBody = jsonData

            print("📤 请求体大小: \(jsonData.count) bytes")

            let (data, response) = try await urlSession.data(for: request)

            print("📥 收到响应，数据大小: \(data.count) bytes")

            // 检查HTTP状态码
            if let httpResponse = response as? HTTPURLResponse {
                print("📊 HTTP状态码: \(httpResponse.statusCode)")
                if httpResponse.statusCode != 200 {
                    // 尝试解析错误响应
                    if let errorString = String(data: data, encoding: .utf8) {
                        print("❌ 错误响应内容: \(errorString)")
                    }
                    return "API请求失败，状态码: \(httpResponse.statusCode)"
                }
            }

            // 尝试解析响应
            do {
                let apiResponse = try JSONDecoder().decode(APIResponse.self, from: data)
                let content = apiResponse.choices.first?.message.content ?? "无响应内容"
                print("✅ 解析成功，内容长度: \(content.count)")
                return content
            } catch {
                print("❌ JSON解析失败: \(error)")
                // 打印原始响应以便调试
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📄 原始响应: \(responseString)")
                }
                return "响应解析失败: \(error.localizedDescription)"
            }

        } catch {
            print("❌ 网络请求失败: \(error)")
            return "网络请求失败: \(error.localizedDescription)"
        }
    }
    

    
    // MARK: - 重新连接
    func reconnect() async {
        await connectAndGreet()
    }

    // MARK: - 加载最近的历史记录
    func loadRecentHistory() async {
        guard let historyManager = historyManager else {
            print("❌ HistoryManager未初始化")
            return
        }

        // 检查数据库状态
        if !historyManager.checkDatabaseStatus() {
            print("❌ 数据库状态异常，跳过历史记录加载")
            await connectAndGreet()
            return
        }

        await historyManager.loadRecentMessages()

        // 将历史记录转换为ChatMessage并显示
        let historyMessages = historyManager.recentMessages.map { $0.toChatMessage() }

        print("📚 历史记录加载完成，共 \(historyMessages.count) 条消息")

        // 如果没有历史记录，显示欢迎消息
        if historyMessages.isEmpty {
            print("📝 没有历史记录，显示欢迎消息")
            await connectAndGreet()
        } else {
            messages = historyMessages
            connectionStatus = .connected
            print("✅ 历史记录已加载到聊天界面")
        }
    }

    // MARK: - 加载更多历史记录
    func loadMoreHistory() async {
        guard let historyManager = historyManager else {
            print("❌ HistoryManager未初始化")
            return
        }

        // 检查数据库状态
        if !historyManager.checkDatabaseStatus() {
            print("❌ 数据库状态异常，无法加载更多历史记录")
            return
        }

        let oldCount = messages.count
        let oldHistoryCount = historyManager.recentMessages.count

        await historyManager.loadMoreHistory()

        // 直接使用历史管理器中的所有消息，它已经正确处理了分页
        let allHistoryMessages = historyManager.recentMessages.map { $0.toChatMessage() }
        messages = allHistoryMessages

        let newHistoryCount = historyManager.recentMessages.count
        let loadedCount = newHistoryCount - oldHistoryCount

        print("✅ 加载了 \(loadedCount) 条更多历史消息，当前总消息数: \(messages.count)")
    }





    // MARK: - 助手功能 (攻略规划等)
    func getAssistantHelp(_ query: String) async -> String {
        let assistantPrompt = """
        帮我解决这个问题：\(query)

        简单说说就行，别太长。
        """

        return await sendMessage(assistantPrompt, useRolePlayingModel: true)
    }

    // MARK: - 表情包支持
    func sendEmojiMessage(_ emoji: String) async {
        let userMessage = ChatMessage(content: emoji, isFromUser: true, messageType: .emoji)
        messages.append(userMessage)

        isLoading = true

        // 使用多智能体系统处理表情消息
        if let multiAgentService = multiAgentService {
            let response = await multiAgentService.processEmojiMessage(emoji)

            // 创建AI回复消息
            let aiMessage = ChatMessage(content: response.content, isFromUser: false, messageType: .text)
            messages.append(aiMessage)

            // 保存到历史记录
            await historyManager?.saveMessage(userMessage)
            await historyManager?.saveMessage(aiMessage)

            // 如果有推荐的TTS情感音色，应用它
            if let ttsEmotion = response.ttsEmotion {
                print("🎵 推荐TTS音色: \(ttsEmotion.description)")
            }

        } else {
            // 回退到原有逻辑
            let response = await sendMessage("我发了个表情：\(emoji)，你随便回个什么", useRolePlayingModel: true)

            if !response.isEmpty {
                let aiMessage = ChatMessage(content: response, isFromUser: false, messageType: .text)
                messages.append(aiMessage)

                // 保存到历史记录
                await historyManager?.saveMessage(userMessage)
                await historyManager?.saveMessage(aiMessage)
            }
        }

        isLoading = false
    }

    // MARK: - 多智能体系统状态

    /// 获取多智能体系统状态摘要
    func getMultiAgentSystemStatus() -> String {
        return multiAgentService?.getSystemStatusSummary() ?? "多智能体系统未初始化"
    }

    /// 切换多智能体模式
    func toggleMultiAgentMode() {
        multiAgentService?.toggleMultiAgentMode()
    }

    /// 检查多智能体系统是否可用
    var isMultiAgentSystemAvailable: Bool {
        return multiAgentService != nil
    }

    // MARK: - ASR服务设置
    private func setupASRService() {
        guard let asrService = asrService else { return }

        // 设置ASR回调
        asrService.onFinalResult = { [weak self] text in
            Task { @MainActor in
                // 当语音识别完成时，自动发送识别的文本
                await self?.sendTextMessage(text)
            }
        }

        asrService.onPartialResult = { [weak self] text in
            Task { @MainActor in
                // 可以在这里显示实时识别结果
                print("🎤 实时识别: \(text)")
            }
        }

        asrService.onError = { [weak self] error in
            Task { @MainActor in
                print("❌ ASR错误: \(error)")
                // 可以在这里显示错误提示
            }
        }
    }

    // MARK: - 开始语音识别
    func startVoiceRecognition() async {
        guard let asrService = asrService else { return }
        await asrService.startRecording()
    }

    // MARK: - 停止语音识别
    func stopVoiceRecognition() {
        asrService?.stopRecording()
    }

    // MARK: - 辅助方法
    private func convertImageToBase64URL(_ image: UIImage) -> String {
        guard let imageData = image.jpegData(compressionQuality: 0.7) else {
            return ""
        }
        let base64Image = imageData.base64EncodedString()
        return "data:image/jpeg;base64,\(base64Image)"
    }
}

// MARK: - 预览用的模拟数据
extension AIService {
    static let preview: AIService = {
        let service = AIService()
        service.messages = [
            ChatMessage(content: "嗨！今天想去哪玩呀？😊", isFromUser: false),
            ChatMessage(content: "想去海边看看", isFromUser: true),
            ChatMessage(content: "哇海边！天气这么好去海边绝了", isFromUser: false),
            ChatMessage(content: "对吧！正好拍照", isFromUser: true),
            ChatMessage(content: "记得防晒哦～不然回来跟鬼子似的哈哈", isFromUser: false)
        ]
        service.connectionStatus = .connected
        return service
    }()
}

// MARK: - AI角色设定管理扩展
extension AIService {
    // MARK: - AI角色设定管理
    private func setupRoleSettingsDatabase() {
        do {
            let url = URL.applicationSupportDirectory.appending(path: "AIRoleSettings.store")
            let config = ModelConfiguration(
                "AIRoleSettings",
                schema: Schema([AIRoleSettings.self]),
                url: url
            )
            let container = try ModelContainer(for: AIRoleSettings.self, configurations: config)
            self.roleSettingsContext = ModelContext(container)
            print("✅ AI角色设定数据库初始化成功")
        } catch {
            print("❌ AI角色设定数据库初始化失败: \(error)")
        }
    }

    private func loadRoleSettings() async {
        guard let context = roleSettingsContext else { return }

        do {
            let descriptor = FetchDescriptor<AIRoleSettings>(
                sortBy: [SortDescriptor(\.lastModified, order: .reverse)]
            )
            let settings = try context.fetch(descriptor)

            await MainActor.run {
                if let latestSetting = settings.first {
                    self.currentSystemPrompt = latestSetting.systemPrompt
                    print("✅ 加载AI角色设定: \(latestSetting.systemPrompt.prefix(50))...")
                } else {
                    // 如果没有设定，创建默认设定
                    self.currentSystemPrompt = AIRoleSettings.defaultPrompt
                    self.saveRoleSettings(AIRoleSettings.defaultPrompt)
                    print("✅ 创建默认AI角色设定")
                }
            }
        } catch {
            await MainActor.run {
                self.currentSystemPrompt = AIRoleSettings.defaultPrompt
                print("❌ 加载AI角色设定失败，使用默认设定: \(error)")
            }
        }
    }

    func saveRoleSettings(_ prompt: String) {
        guard let context = roleSettingsContext else { return }

        let newSettings = AIRoleSettings(systemPrompt: prompt)
        context.insert(newSettings)

        do {
            try context.save()
            currentSystemPrompt = prompt
            print("✅ 保存AI角色设定成功")
        } catch {
            print("❌ 保存AI角色设定失败: \(error)")
        }
    }

    func getCurrentSystemPrompt() -> String {
        return currentSystemPrompt
    }
}
