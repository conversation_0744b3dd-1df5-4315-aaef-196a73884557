//
//  LongTermMemoryManager.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation
import SwiftData
import SwiftUI

// MARK: - 长期记忆管理器
/// 负责智能检索和上下文理解的长期记忆系统
/// 基于现有的ChatHistoryManager扩展，实现RAG（检索增强生成）功能
@MainActor
class LongTermMemoryManager: ObservableObject {
    
    // MARK: - 属性
    
    /// 基础历史记录管理器
    private var historyManager: ChatHistoryManager?
    
    /// 系统就绪状态
    @Published var isReady: Bool = false
    
    /// 记忆重要性缓存
    private var memoryImportanceCache: [UUID: Double] = [:]

    /// 最近检索的记忆
    @Published var recentRetrievedMemories: [ChatHistory] = []

    /// 检索结果缓存
    private var retrievalCache: [String: [ChatHistory]] = [:]

    /// 缓存时间戳
    private var cacheTimestamps: [String: Date] = [:]

    /// 缓存过期时间（秒）
    private let cacheExpirationTime: TimeInterval = 300 // 5分钟
    
    // MARK: - 初始化
    
    init() {
        print("🧠 初始化长期记忆管理器...")
    }
    
    /// 异步初始化系统
    func initialize() async {
        print("🔄 开始初始化长期记忆系统...")
        
        // 初始化历史记录管理器
        historyManager = ChatHistoryManager()
        
        // 检查数据库状态
        guard let historyManager = historyManager,
              historyManager.checkDatabaseStatus() else {
            print("❌ 历史记录数据库状态异常")
            return
        }
        
        // 加载最近的记忆
        await historyManager.loadRecentMessages()
        
        // 构建记忆重要性缓存
        await buildImportanceCache()
        
        isReady = true
        print("✅ 长期记忆系统初始化完成，共加载 \(getTotalMemoryCount()) 条记忆")
    }
    
    // MARK: - 智能检索方法
    
    /// 根据查询内容检索相关记忆
    /// - Parameters:
    ///   - query: 查询内容
    ///   - limit: 返回结果数量限制
    /// - Returns: 相关的历史记录数组
    func retrieveRelevantMemories(_ query: String, limit: Int = 5) async -> [ChatHistory] {
        guard let historyManager = historyManager, isReady else {
            print("⚠️ 长期记忆系统未就绪")
            return []
        }

        // 生成缓存键
        let cacheKey = "\(query)_\(limit)"

        // 检查缓存是否有效
        if let cachedResult = getCachedResult(for: cacheKey) {
            print("🎯 使用缓存的检索结果 (\(cachedResult.count) 条记忆)")
            return cachedResult
        }

        print("🔍 检索与 '\(query)' 相关的记忆...")

        // 获取所有历史记录
        let allMemories = historyManager.recentMessages

        // 使用多种策略进行智能检索
        var scoredMemories: [(ChatHistory, Double)] = []

        for memory in allMemories {
            let score = calculateRelevanceScore(memory: memory, query: query)
            if score > 0.1 { // 只保留相关性大于0.1的记忆
                scoredMemories.append((memory, score))
            }
        }

        // 按相关性分数排序并取前N个
        let relevantMemories = scoredMemories
            .sorted { $0.1 > $1.1 }
            .prefix(limit)
            .map { $0.0 }

        let result = Array(relevantMemories)

        // 缓存结果
        cacheResult(result, for: cacheKey)

        recentRetrievedMemories = result

        print("✅ 检索到 \(result.count) 条相关记忆")
        return result
    }
    
    /// 根据时间范围检索上下文记忆
    func retrieveContextualMemories(_ timeRange: DateInterval) async -> [ChatHistory] {
        guard let historyManager = historyManager, isReady else { return [] }
        
        let contextualMemories = historyManager.recentMessages.filter { memory in
            timeRange.contains(memory.timestamp)
        }
        
        print("📅 检索到时间范围内的 \(contextualMemories.count) 条记忆")
        return contextualMemories
    }
    
    /// 生成RAG上下文字符串
    func generateRAGContext(_ memories: [ChatHistory]) -> String {
        guard !memories.isEmpty else { return "" }
        
        let contextLines = memories.prefix(10).map { memory in
            let timeStr = memory.timestamp.formatted(date: .abbreviated, time: .shortened)
            let sender = memory.isFromUser ? "用户" : "AI"
            let content = memory.imageDescription ?? memory.content
            return "[\(timeStr)] \(sender): \(content)"
        }
        
        let context = """
        === 相关对话历史 ===
        \(contextLines.joined(separator: "\n"))
        === 历史结束 ===
        """
        
        print("📝 生成RAG上下文，包含 \(memories.count) 条记忆")
        return context
    }
    
    // MARK: - 记忆重要性评估
    
    /// 计算记忆的相关性分数
    private func calculateRelevanceScore(memory: ChatHistory, query: String) -> Double {
        let content = (memory.imageDescription ?? memory.content).lowercased()
        let queryLower = query.lowercased()
        
        var score: Double = 0.0
        
        // 1. 关键词匹配分数 (权重: 0.4)
        let keywordScore = calculateKeywordMatchScore(content: content, query: queryLower)
        score += keywordScore * 0.4
        
        // 2. 时间新近性分数 (权重: 0.3)
        let timeScore = calculateTimeRelevanceScore(timestamp: memory.timestamp)
        score += timeScore * 0.3
        
        // 3. 消息类型分数 (权重: 0.2)
        let typeScore = calculateMessageTypeScore(messageType: memory.messageType)
        score += typeScore * 0.2
        
        // 4. 缓存的重要性分数 (权重: 0.1)
        let importanceScore = memoryImportanceCache[memory.id] ?? 0.5
        score += importanceScore * 0.1
        
        return min(score, 1.0) // 确保分数不超过1.0
    }
    
    /// 计算关键词匹配分数
    private func calculateKeywordMatchScore(content: String, query: String) -> Double {
        let queryWords = query.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        
        guard !queryWords.isEmpty else { return 0.0 }
        
        let matchCount = queryWords.reduce(0) { count, word in
            return content.contains(word) ? count + 1 : count
        }
        
        return Double(matchCount) / Double(queryWords.count)
    }
    
    /// 计算时间相关性分数（越新的记忆分数越高）
    private func calculateTimeRelevanceScore(timestamp: Date) -> Double {
        let now = Date()
        let timeDiff = now.timeIntervalSince(timestamp)
        let daysDiff = timeDiff / (24 * 60 * 60)
        
        // 使用指数衰减函数，7天内的记忆保持较高分数
        return exp(-daysDiff / 7.0)
    }
    
    /// 计算消息类型分数
    private func calculateMessageTypeScore(messageType: String) -> Double {
        switch messageType {
        case "text": return 1.0
        case "image": return 0.8
        case "emoji": return 0.3
        case "system": return 0.1
        default: return 0.5
        }
    }
    
    /// 构建记忆重要性缓存
    private func buildImportanceCache() async {
        guard let historyManager = historyManager else { return }
        
        print("🔄 构建记忆重要性缓存...")
        
        for memory in historyManager.recentMessages {
            let importance = calculateMemoryImportance(memory)
            memoryImportanceCache[memory.id] = importance
        }
        
        print("✅ 记忆重要性缓存构建完成，共 \(memoryImportanceCache.count) 条记忆")
    }
    
    /// 计算单条记忆的重要性
    private func calculateMemoryImportance(_ memory: ChatHistory) -> Double {
        var importance: Double = 0.5 // 基础重要性
        
        let content = (memory.imageDescription ?? memory.content).lowercased()
        
        // 包含情感词汇的记忆更重要
        let emotionKeywords = ["开心", "难过", "生气", "担心", "兴奋", "失望", "感动", "害怕"]
        if emotionKeywords.contains(where: { content.contains($0) }) {
            importance += 0.3
        }
        
        // 包含旅行相关词汇的记忆更重要
        let travelKeywords = ["旅行", "景点", "酒店", "机票", "攻略", "路线", "地图"]
        if travelKeywords.contains(where: { content.contains($0) }) {
            importance += 0.2
        }
        
        // 较长的消息通常更重要
        if content.count > 50 {
            importance += 0.1
        }
        
        return min(importance, 1.0)
    }
    
    // MARK: - 缓存管理

    /// 获取缓存结果
    private func getCachedResult(for key: String) -> [ChatHistory]? {
        // 检查缓存是否存在且未过期
        guard let cachedResult = retrievalCache[key],
              let timestamp = cacheTimestamps[key],
              Date().timeIntervalSince(timestamp) < cacheExpirationTime else {
            return nil
        }

        return cachedResult
    }

    /// 缓存检索结果
    private func cacheResult(_ result: [ChatHistory], for key: String) {
        retrievalCache[key] = result
        cacheTimestamps[key] = Date()

        // 清理过期缓存
        cleanExpiredCache()
    }

    /// 清理过期缓存
    private func cleanExpiredCache() {
        let now = Date()
        let expiredKeys = cacheTimestamps.compactMap { key, timestamp in
            now.timeIntervalSince(timestamp) >= cacheExpirationTime ? key : nil
        }

        for key in expiredKeys {
            retrievalCache.removeValue(forKey: key)
            cacheTimestamps.removeValue(forKey: key)
        }

        if !expiredKeys.isEmpty {
            print("🧹 清理了 \(expiredKeys.count) 个过期缓存")
        }
    }

    // MARK: - 上下文更新回调

    /// 当对话上下文更新时的回调
    func onContextUpdated(_ context: ConversationContext) async {
        // 可以在这里实现上下文变化的响应逻辑
        print("🔄 长期记忆系统收到上下文更新通知")

        // 清理缓存，因为上下文已更新
        retrievalCache.removeAll()
        cacheTimestamps.removeAll()
        print("🧹 已清理检索缓存（上下文更新）")
    }
    
    // MARK: - 状态查询方法
    
    /// 获取总记忆数量
    func getTotalMemoryCount() -> Int {
        return historyManager?.recentMessages.count ?? 0
    }
    
    /// 获取系统状态摘要
    func getStatusSummary() -> String {
        let status = isReady ? "✅ 就绪" : "⏳ 初始化中"
        let memoryCount = getTotalMemoryCount()
        let cacheSize = memoryImportanceCache.count
        
        return """
        长期记忆状态: \(status)
        总记忆数量: \(memoryCount)
        重要性缓存: \(cacheSize)
        最近检索: \(recentRetrievedMemories.count) 条
        """
    }
}
