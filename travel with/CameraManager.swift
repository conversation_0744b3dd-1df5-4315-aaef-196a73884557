//
//  CameraManager.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/1.
//

import SwiftUI
import AVFoundation

// MARK: - 摄像头管理器
class CameraManager: NSObject, ObservableObject {
    @Published var isSessionRunning = false
    @Published var hasPermission = false
    @Published var currentCameraPosition: AVCaptureDevice.Position = .front
    
    private let session = AVCaptureSession()
    private var videoDeviceInput: AVCaptureDeviceInput?
    private let sessionQueue = DispatchQueue(label: "camera.session.queue")
    
    override init() {
        super.init()
        checkPermissions()
    }
    
    // MARK: - 权限检查
    private func checkPermissions() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            hasPermission = true
        case .notDetermined:
            requestPermission()
        case .denied, .restricted:
            hasPermission = false
        @unknown default:
            hasPermission = false
        }
    }
    
    // MARK: - 请求权限
    private func requestPermission() {
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                self?.hasPermission = granted
                if granted {
                    self?.setupSession()
                }
            }
        }
    }
    
    // MARK: - 设置会话
    private func setupSession() {
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            self.session.beginConfiguration()
            
            // 设置会话预设
            if self.session.canSetSessionPreset(.hd1280x720) {
                self.session.sessionPreset = .hd1280x720
            }
            
            // 添加视频输入
            self.addVideoInput()
            
            self.session.commitConfiguration()
        }
    }
    
    // MARK: - 添加视频输入
    private func addVideoInput() {
        guard let videoDevice = AVCaptureDevice.default(.builtInWideAngleCamera, 
                                                       for: .video, 
                                                       position: currentCameraPosition) else {
            print("无法获取摄像头设备")
            return
        }
        
        do {
            let videoDeviceInput = try AVCaptureDeviceInput(device: videoDevice)
            
            if session.canAddInput(videoDeviceInput) {
                session.addInput(videoDeviceInput)
                self.videoDeviceInput = videoDeviceInput
            } else {
                print("无法添加视频输入")
            }
        } catch {
            print("创建视频输入失败: \(error)")
        }
    }
    
    // MARK: - 开始会话
    func startSession() {
        guard hasPermission else {
            checkPermissions()
            return
        }
        
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            if !self.session.isRunning {
                if self.videoDeviceInput == nil {
                    self.setupSession()
                }
                self.session.startRunning()
                
                DispatchQueue.main.async {
                    self.isSessionRunning = self.session.isRunning
                }
            }
        }
    }
    
    // MARK: - 停止会话
    func stopSession() {
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            if self.session.isRunning {
                self.session.stopRunning()
                
                DispatchQueue.main.async {
                    self.isSessionRunning = false
                }
            }
        }
    }
    
    // MARK: - 切换摄像头
    func switchCamera() {
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            self.session.beginConfiguration()
            
            // 移除当前输入
            if let currentInput = self.videoDeviceInput {
                self.session.removeInput(currentInput)
            }
            
            // 切换摄像头位置
            self.currentCameraPosition = self.currentCameraPosition == .front ? .back : .front
            
            // 添加新的输入
            self.addVideoInput()
            
            self.session.commitConfiguration()
        }
    }
    
    // MARK: - 获取预览层
    func getPreviewLayer() -> AVCaptureVideoPreviewLayer {
        let previewLayer = AVCaptureVideoPreviewLayer(session: session)
        previewLayer.videoGravity = .resizeAspectFill // 使用填充模式进行中心裁剪
        return previewLayer
    }
}

// MARK: - 摄像头预览视图
struct CameraPreviewView: UIViewRepresentable {
    let cameraManager: CameraManager
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        view.backgroundColor = .black
        
        let previewLayer = cameraManager.getPreviewLayer()
        previewLayer.frame = view.bounds
        view.layer.addSublayer(previewLayer)
        
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        if let previewLayer = uiView.layer.sublayers?.first as? AVCaptureVideoPreviewLayer {
            DispatchQueue.main.async {
                previewLayer.frame = uiView.bounds
            }
        }
    }
}

// MARK: - 预览
#Preview {
    VideoCallView()
        .environmentObject(AIService.preview)
}
