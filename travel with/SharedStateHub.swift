//
//  SharedStateHub.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation
import SwiftUI
import SwiftData

// MARK: - 共享状态中心
/// 多智能体系统的数据中枢，负责统一管理所有智能体的共享状态
/// 这是实现智能体协同工作的核心组件
@MainActor
class SharedStateHub: ObservableObject {
    
    // MARK: - 核心管理器
    
    /// 长期记忆管理器 - 负责智能检索和上下文理解
    @Published var longTermMemory: LongTermMemoryManager
    
    /// AI人格管理器 - 管理AI的核心人格和动态状态
    @Published var aiPersonality: AIPersonalityManager
    
    /// AI生活日程管理器 - 管理AI的虚拟生活和日程安排
    @Published var aiLifeSchedule: AILifeScheduleManager

    /// AI自我管理系统 - 管理AI的自主学习和人格进化
    @Published var aiSelfManagement: AISelfManagementSystem

    /// 当前对话上下文 - 维护当前对话的状态信息
    @Published var currentContext: ConversationContext
    
    // MARK: - 系统状态
    
    /// 系统初始化状态
    @Published var isInitialized: Bool = false
    
    /// 最后更新时间
    @Published var lastUpdateTime: Date = Date()
    
    // MARK: - 初始化
    
    init() {
        print("🏗️ 初始化共享状态中心...")
        
        // 初始化各个管理器
        self.longTermMemory = LongTermMemoryManager()
        self.aiPersonality = AIPersonalityManager()
        self.aiLifeSchedule = AILifeScheduleManager()
        self.aiSelfManagement = AISelfManagementSystem()
        self.currentContext = ConversationContext()
        
        // 启动初始化流程
        Task {
            await initializeSystem()
        }
    }
    
    // MARK: - 系统初始化
    
    /// 初始化整个共享状态系统
    private func initializeSystem() async {
        print("🔄 开始初始化共享状态系统...")
        
        do {
            // 1. 初始化长期记忆系统
            await longTermMemory.initialize()
            print("✅ 长期记忆系统初始化完成")
            
            // 2. 初始化AI人格系统
            await aiPersonality.initialize()
            print("✅ AI人格系统初始化完成")
            
            // 3. 初始化AI生活日程系统
            await aiLifeSchedule.initialize()
            print("✅ AI生活日程系统初始化完成")
            
            // 4. 初始化对话上下文
            currentContext.initialize()
            print("✅ 对话上下文初始化完成")
            
            // 标记系统已初始化
            isInitialized = true
            lastUpdateTime = Date()
            
            print("🎉 共享状态中心初始化完成！")
            
        } catch {
            print("❌ 共享状态中心初始化失败: \(error)")
        }
    }
    
    // MARK: - 状态更新方法
    
    /// 更新对话上下文
    func updateConversationContext(userMessage: String, aiResponse: String? = nil) {
        currentContext.addMessage(userMessage: userMessage, aiResponse: aiResponse)
        lastUpdateTime = Date()
        
        // 通知所有管理器上下文已更新
        Task {
            await longTermMemory.onContextUpdated(currentContext)
            await aiPersonality.onContextUpdated(currentContext)
            await aiLifeSchedule.onContextUpdated(currentContext)
        }
    }
    
    /// 更新AI情感状态
    func updateAIEmotion(_ emotion: EmotionState) {
        aiPersonality.updateEmotion(emotion)
        lastUpdateTime = Date()
        print("💭 AI情感状态已更新: \(emotion.primary)")
    }
    
    /// 更新AI当前活动
    func updateCurrentActivity(_ activity: LifeActivity) {
        aiLifeSchedule.updateCurrentActivity(activity)
        lastUpdateTime = Date()
        print("🎯 AI当前活动已更新: \(activity.name)")
    }
    
    // MARK: - 数据获取方法
    
    /// 获取当前完整的AI人格描述（核心人格 + 动态状态）
    func getCurrentPersonality() -> String {
        return aiPersonality.generateCurrentPersonality()
    }
    
    /// 获取当前AI生活状态描述
    func getCurrentLifeStatus() -> String {
        return aiLifeSchedule.getCurrentLifeStatus()
    }
    
    /// 获取相关的历史记忆
    func getRelevantMemories(for query: String, limit: Int = 5) async -> [ChatHistory] {
        return await longTermMemory.retrieveRelevantMemories(query, limit: limit)
    }
    
    /// 生成RAG上下文
    func generateRAGContext(for query: String) async -> String {
        let memories = await getRelevantMemories(for: query)
        return longTermMemory.generateRAGContext(memories)
    }
    
    // MARK: - 系统状态检查
    
    /// 检查系统是否准备就绪
    func isSystemReady() -> Bool {
        return isInitialized && 
               longTermMemory.isReady && 
               aiPersonality.isReady && 
               aiLifeSchedule.isReady
    }
    
    /// 获取系统状态摘要
    func getSystemStatusSummary() -> String {
        let status = isSystemReady() ? "✅ 就绪" : "⏳ 初始化中"
        let memoryCount = longTermMemory.getTotalMemoryCount()
        let currentEmotion = aiPersonality.currentEmotion.primary
        let currentActivity = aiLifeSchedule.currentActivity?.name ?? "空闲"
        
        return """
        系统状态: \(status)
        记忆数量: \(memoryCount)
        当前情感: \(currentEmotion)
        当前活动: \(currentActivity)
        最后更新: \(lastUpdateTime.formatted(date: .omitted, time: .shortened))
        """
    }
}

// MARK: - 对话上下文模型

/// 当前对话上下文，维护对话状态信息
class ConversationContext: ObservableObject {
    @Published var currentTopic: String = ""
    @Published var userIntent: UserIntent = .unknown
    @Published var conversationHistory: [ContextMessage] = []
    @Published var sessionStartTime: Date = Date()
    
    func initialize() {
        sessionStartTime = Date()
        conversationHistory.removeAll()
        print("🔄 对话上下文已初始化")
    }
    
    func addMessage(userMessage: String, aiResponse: String? = nil) {
        let contextMessage = ContextMessage(
            userMessage: userMessage,
            aiResponse: aiResponse,
            timestamp: Date()
        )
        conversationHistory.append(contextMessage)
        
        // 保持最近20条消息
        if conversationHistory.count > 20 {
            conversationHistory.removeFirst()
        }
    }
}

/// 上下文消息模型
struct ContextMessage {
    let userMessage: String
    let aiResponse: String?
    let timestamp: Date
}

// MARK: - 用户意图枚举

enum UserIntent {
    case simpleChat      // 简单聊天
    case deepChat        // 深度对话
    case imageQuery      // 图像查询
    case emotionalSupport // 情感支持
    case travelPlanning  // 旅行规划
    case unknown         // 未知意图
}

// MARK: - 预览支持

extension SharedStateHub {
    /// 用于SwiftUI预览的静态实例
    static let preview: SharedStateHub = {
        let hub = SharedStateHub()
        return hub
    }()
}
