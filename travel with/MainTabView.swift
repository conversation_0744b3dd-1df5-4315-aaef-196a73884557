//
//  MainTabView.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

struct MainTabView: View {
    @StateObject private var aiService = AIService()
    @StateObject private var placeManager = PlaceManager()
    @State private var selectedTab = 2 // 默认选中聊天Tab
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 地图Tab
            MapNavigationView()
                .tabItem {
                    Image(systemName: "map.fill")
                    Text("地图")
                }
                .tag(0)

            // 地点Tab
            PlacesView()
                .tabItem {
                    Image(systemName: "location.circle.fill")
                    Text("地点")
                }
                .tag(1)

            // 聊天Tab (核心+高亮)
            AIChatView()
                .tabItem {
                    Image(systemName: "message.circle.fill")
                    Text("聊天")
                }
                .tag(2)

            // 我的Tab
            ProfileView()
                .tabItem {
                    Image(systemName: "person.crop.circle.fill")
                    Text("我的")
                }
                .tag(3)
        }
        .environmentObject(aiService)
        .environmentObject(placeManager)
        .accentColor(.orange) // 温暖的橙色作为选中颜色
        .onAppear {
            setupTabBarAppearance()
        }
    }
    
    // MARK: - 自定义TabBar样式
    private func setupTabBarAppearance() {
        // 设置TabBar背景为温暖的白色
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground
        
        // 设置选中状态的颜色
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.systemOrange
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemOrange,
            .font: UIFont.systemFont(ofSize: 12, weight: .medium)
        ]
        
        // 设置未选中状态的颜色
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.systemGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.systemGray,
            .font: UIFont.systemFont(ofSize: 12, weight: .regular)
        ]
        
        // 应用样式
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MapNavigationView现在在单独的文件中实现

// AIChatView现在在单独的文件中实现

// ProfileView现在在单独的文件中实现

// MARK: - 预览
#Preview {
    MainTabView()
}
