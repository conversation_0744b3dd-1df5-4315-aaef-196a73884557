//
//  MapNavigationView.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI
import MapKit
import CoreLocation

struct MapNavigationView: View {
    @EnvironmentObject var aiService: AIService
    @EnvironmentObject var placeManager: PlaceManager
    @ObservedObject private var locationManager = SharedLocationManager.shared
    @State private var searchText = ""
    @State private var showingAIPopup = false
    @State private var aiPopupMessage = ""
    @State private var selectedLocation: CLLocationCoordinate2D?

    @State private var isMapLoaded = false
    @State private var searchResults: [MKMapItem] = []
    @State private var showingSearchResults = false
    @State private var currentRoute: MKRoute?
    @State private var isNavigating = false
    @State private var destinationName = ""

    @State private var selectedTransportType: TransportType = .automobile
    @State private var availableRoutes: [RouteOption] = []
    @State private var showingTransportOptions = false
    @State private var showingRideOptions = false
    @State private var showingPlanSelector = false

    // 导航相关状态
    @State private var currentStepIndex = 0
    @State private var navigationSteps: [MKRoute.Step] = []
    @State private var remainingDistance: CLLocationDistance = 0
    @State private var remainingTime: TimeInterval = 0
    @State private var currentInstruction = ""
    @State private var nextInstruction = ""
    @State private var isNavigationMapMode = false
    @State private var navigationTimer: Timer?

    // 角度平滑处理
    @State private var smoothedHeading: Double = 0
    @State private var lastHeading: Double = 0

    enum TransportType: String, CaseIterable {
        case automobile = "开车"
        case walking = "步行"
        case transit = "公交"
        case cycling = "骑车"
        case rideshare = "打车"

        var icon: String {
            switch self {
            case .automobile: return "car.fill"
            case .walking: return "figure.walk"
            case .transit: return "bus.fill"
            case .cycling: return "bicycle"
            case .rideshare: return "car.2.fill"
            }
        }

        var mkTransportType: MKDirectionsTransportType {
            switch self {
            case .automobile: return .automobile
            case .walking: return .walking
            case .transit: return .transit
            case .cycling: return .walking // 使用walking作为骑车的基础，后续自定义处理
            case .rideshare: return .automobile
            }
        }

        var isSupported: Bool {
            switch self {
            case .automobile, .walking: return true
            case .transit: return true // 公交路线支持，但可能在某些地区不可用
            case .cycling: return true // 骑车路线，使用步行路线作为参考
            case .rideshare: return true // 打车通过第三方应用
            }
        }
    }

    struct RouteOption {
        let route: MKRoute
        let transportType: TransportType
        let duration: TimeInterval
        let distance: CLLocationDistance
        let description: String
    }
    @State private var cameraPosition = MapCameraPosition.region(
        MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074), // 北京
            span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
        )
    )
    
    var body: some View {
        NavigationView {
            ZStack {
                // 地图主体
                mapView

                // 地图加载指示器
                if !isMapLoaded {
                    VStack {
                        ProgressView()
                            .scaleEffect(1.5)
                        Text("正在加载地图...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding(.top, 8)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color(.systemBackground).opacity(0.8))
                }
                
                // 顶部区域：搜索栏或导航信息
                VStack {
                    if isNavigating {
                        // 导航信息面板
                        NavigationInfoPanel(
                            currentInstruction: currentInstruction,
                            nextInstruction: nextInstruction,
                            remainingDistance: remainingDistance,
                            remainingTime: remainingTime,
                            onStopNavigation: stopNavigation
                        )
                        .padding(.horizontal)
                        .padding(.top, 8)
                    } else {
                        // 搜索栏和计划选择器
                        HStack(spacing: 12) {
                            MapSearchBarView(searchText: $searchText, onSearch: performSearch)

                            // 计划选择器按钮
                            Button(action: {
                                showingPlanSelector.toggle()
                            }) {
                                Image(systemName: "list.bullet.circle.fill")
                                    .font(.title2)
                                    .foregroundColor(.blue)
                                    .background(Color(.systemBackground))
                                    .clipShape(Circle())
                                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                            }
                        }
                        .padding(.horizontal)
                        .padding(.top, 8)

                        // 搜索结果列表
                        if showingSearchResults && !searchResults.isEmpty {
                            SearchResultsView(
                                searchResults: searchResults,
                                onSelectResult: { mapItem in
                                    selectDestination(mapItem)
                                    showingSearchResults = false
                                },
                                onDismiss: {
                                    showingSearchResults = false
                                }
                            )
                            .transition(.move(edge: .top).combined(with: .opacity))
                        }
                    }

                    Spacer()
                    
                    // 底部控制栏
                    BottomControlsView(
                        isNavigating: isNavigating,
                        hasDestination: selectedLocation != nil,
                        selectedTransportType: selectedTransportType,
                        availableRoutes: availableRoutes,
                        onLocationCenter: {
                            centerOnUserLocation()
                        },
                        onStartNavigation: {
                            startNavigation()
                        },
                        onStopNavigation: {
                            stopNavigation()
                        },
                        onTransportTypeChanged: { transportType in
                            selectedTransportType = transportType
                            if selectedLocation != nil {
                                calculateAllRoutes(to: selectedLocation!)
                            }
                        },
                        onShowRideOptions: {
                            showingRideOptions = true
                        }
                    )
                    .padding(.horizontal)
                    .padding(.bottom, 8)
                }
                
                // AI消息弹窗
                if showingAIPopup {
                    AIMessageBannerView(
                        message: aiPopupMessage,
                        onDismiss: {
                            withAnimation(.spring()) {
                                showingAIPopup = false
                            }
                        }
                    )
                    .transition(.move(edge: .top).combined(with: .opacity))
                    .zIndex(1)
                }
            }
            .navigationTitle("地图导航")
            .navigationBarTitleDisplayMode(.inline)

            .sheet(isPresented: $showingRideOptions) {
                RideOptionsView(
                    destination: selectedLocation,
                    destinationName: destinationName
                )
            }
            .sheet(isPresented: $showingPlanSelector) {
                PlanSelectorView(placeManager: placeManager)
            }
        }
    }

    // MARK: - 地图视图组件
    private var mapView: some View {
        Map(position: $cameraPosition) {
            userLocationAnnotation
            if !isNavigating {
                searchResultAnnotations
            }
            destinationAnnotation
            placeAnnotations
            routePolyline
            navigationHighlight
        }
        .mapStyle(.standard)
        .mapControls {
            if !isNavigating {
                MapUserLocationButton()
                MapCompass()
                MapScaleView()
            }
        }
        .ignoresSafeArea(edges: .bottom)
        .onAppear {
            print("🗺️ 地图视图出现")
            locationManager.requestPermission()
            locationManager.startAllUpdates()
            // 延迟标记地图已加载，给地图一些时间来渲染
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                isMapLoaded = true
                // 移除AI提醒功能
            }
        }
        .onChange(of: locationManager.userLocation) { _, newValue in
            if let location = newValue {
                print("🗺️ 更新地图中心到用户位置")
                withAnimation(.easeInOut(duration: 1.0)) {
                    cameraPosition = .region(MKCoordinateRegion(
                        center: location.coordinate,
                        span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
                    ))
                }
            }
        }
    }

    // MARK: - 用户位置标注
    @MapContentBuilder
    private var userLocationAnnotation: some MapContent {
        if let userLocation = locationManager.userLocation {
            Annotation("我的位置", coordinate: userLocation.coordinate) {
                Circle()
                    .fill(.blue)
                    .frame(width: isNavigating ? 24 : 20, height: isNavigating ? 24 : 20)
                    .overlay(
                        Circle()
                            .stroke(.white, lineWidth: 3)
                    )
                    .overlay(
                        // 导航时显示方向箭头
                        isNavigating ?
                        Image(systemName: "location.north.fill")
                            .font(.caption)
                            .foregroundColor(.white)
                            .rotationEffect(.degrees(getArrowRotation()))
                            .animation(.easeInOut(duration: 0.5), value: smoothedHeading)
                        : nil
                    )
                    .shadow(radius: isNavigating ? 4 : 2)
            }
        }
    }

    // MARK: - 地点标注
    @MapContentBuilder
    private var placeAnnotations: some MapContent {
        // 显示所有地点，不受当前选中计划限制
        ForEach(getPlacesToShow(), id: \.id) { place in
            Annotation(place.name, coordinate: place.coordinate) {
                Button(action: {
                    // 点击地点标注时，可以设置为目的地
                    selectedLocation = place.coordinate
                    destinationName = place.name
                    calculateAllRoutes(to: place.coordinate)
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: "mappin.circle.fill")
                            .font(.title2)
                            .foregroundColor(.red)
                            .background(Color.white)
                            .clipShape(Circle())
                            .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)

                        Text(place.name)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.white.opacity(0.9))
                            .cornerRadius(8)
                            .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)
                    }
                }
                .buttonStyle(.plain)
            }
            .annotationTitles(.hidden)
        }
    }

    // MARK: - 获取要显示的地点
    private func getPlacesToShow() -> [Place] {
        if placeManager.selectedPlan?.name == "全部地点" {
            // 如果选中"全部地点"，显示所有地点
            return placeManager.getAllPlaces()
        } else {
            // 如果选中具体计划，只显示该计划的地点
            return placeManager.getPlaces(for: placeManager.selectedPlan)
        }
    }

    // MARK: - 角度平滑处理
    private func smoothAngle(_ newAngle: Double) -> Double {
        let diff = angleDifference(from: lastHeading, to: newAngle)

        // 如果角度差异超过180度，说明发生了跳跃
        if abs(diff) > 180 {
            // 计算最短路径的角度
            let shortestDiff = diff > 0 ? diff - 360 : diff + 360
            smoothedHeading = lastHeading + shortestDiff
        } else {
            smoothedHeading = newAngle
        }

        lastHeading = newAngle
        return smoothedHeading
    }

    // 计算两个角度之间的差异
    private func angleDifference(from angle1: Double, to angle2: Double) -> Double {
        var diff = angle2 - angle1
        while diff > 180 { diff -= 360 }
        while diff < -180 { diff += 360 }
        return diff
    }

    // MARK: - 计算箭头旋转角度
    private func getArrowRotation() -> Double {
        if isNavigating {
            let rawAngle: Double

            // 导航时优先使用移动方向，如果移动方向无效或用户静止则使用设备朝向
            if let userLocation = locationManager.userLocation,
               userLocation.course >= 0 && userLocation.speed > 0.5 { // 速度大于0.5m/s才使用移动方向
                print("🧭 使用移动方向: \(String(format: "%.1f", locationManager.userCourse))° (速度: \(String(format: "%.1f", userLocation.speed))m/s)")
                rawAngle = locationManager.userCourse
            } else {
                print("🧭 使用设备朝向: \(String(format: "%.1f", locationManager.deviceHeading))°")
                rawAngle = locationManager.deviceHeading
            }

            // 应用角度平滑处理
            let smoothedAngle = smoothAngle(rawAngle)
            print("🧭 平滑后角度: \(String(format: "%.1f", smoothedAngle))° (原始: \(String(format: "%.1f", rawAngle))°)")
            return smoothedAngle
        }
        return 0
    }

    // MARK: - 搜索结果标注
    @MapContentBuilder
    private var searchResultAnnotations: some MapContent {
        ForEach(searchResults, id: \.self) { mapItem in
            if let coordinate = mapItem.placemark.location?.coordinate {
                Annotation(mapItem.name ?? "地点", coordinate: coordinate) {
                    Button(action: {
                        selectDestination(mapItem)
                    }) {
                        VStack {
                            Image(systemName: "mappin.circle.fill")
                                .foregroundColor(.red)
                                .font(.title2)
                            Text(mapItem.name ?? "地点")
                                .font(.caption)
                                .padding(4)
                                .background(Color.white.opacity(0.9))
                                .cornerRadius(4)
                        }
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }

    // MARK: - 目的地标注
    @MapContentBuilder
    private var destinationAnnotation: some MapContent {
        if let selectedLocation = selectedLocation {
            Annotation(destinationName.isEmpty ? "目的地" : destinationName, coordinate: selectedLocation) {
                VStack {
                    Image(systemName: "flag.circle.fill")
                        .foregroundColor(.green)
                        .font(.title)
                    Text(destinationName.isEmpty ? "目的地" : destinationName)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .padding(4)
                        .background(Color.green.opacity(0.1))
                        .cornerRadius(4)
                }
            }
        }
    }

    // MARK: - 路线多边形
    @MapContentBuilder
    private var routePolyline: some MapContent {
        if let route = currentRoute {
            MapPolyline(route.polyline)
                .stroke(isNavigating ? .blue.opacity(0.6) : .blue, lineWidth: isNavigating ? 6 : 5)
        }
    }

    // MARK: - 导航高亮
    @MapContentBuilder
    private var navigationHighlight: some MapContent {
        if isNavigating && currentStepIndex < navigationSteps.count {
            let currentStep = navigationSteps[currentStepIndex]
            MapPolyline(currentStep.polyline)
                .stroke(.orange, lineWidth: 10)
        } else {
            EmptyMapContent()
        }
    }
    
    // MARK: - 搜索功能
    private func performSearch(_ query: String) {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }

        print("🔍 搜索: \(query)")

        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = query

        // 设置搜索区域（以用户当前位置为中心，或默认区域）
        if let userLocation = locationManager.userLocation {
            request.region = MKCoordinateRegion(
                center: userLocation.coordinate,
                span: MKCoordinateSpan(latitudeDelta: 0.1, longitudeDelta: 0.1)
            )
        } else {
            // 默认搜索区域（北京）
            request.region = MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
                span: MKCoordinateSpan(latitudeDelta: 0.1, longitudeDelta: 0.1)
            )
        }

        let search = MKLocalSearch(request: request)
        search.start { response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ 搜索错误: \(error.localizedDescription)")
                    return
                }

                guard let response = response else {
                    print("❌ 搜索无结果")
                    return
                }

                print("✅ 找到 \(response.mapItems.count) 个结果")
                self.searchResults = response.mapItems
                withAnimation(.spring()) {
                    self.showingSearchResults = true
                }
            }
        }
    }

    // MARK: - 选择目的地
    private func selectDestination(_ mapItem: MKMapItem) {
        guard let coordinate = mapItem.placemark.location?.coordinate else { return }

        selectedLocation = coordinate
        destinationName = mapItem.name ?? "目的地"
        searchResults.removeAll()

        print("📍 选择目的地: \(destinationName)")

        // 调整地图视角以显示目的地
        withAnimation(.easeInOut(duration: 1.0)) {
            cameraPosition = .region(MKCoordinateRegion(
                center: coordinate,
                span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
            ))
        }

        // 自动计算所有交通方式的路线
        calculateAllRoutes(to: coordinate)
    }

    // MARK: - 计算所有交通方式的路线
    private func calculateAllRoutes(to destination: CLLocationCoordinate2D) {
        guard locationManager.userLocation != nil else {
            print("❌ 无法获取用户位置")
            return
        }

        print("🗺️ 计算多种交通方式的路线...")
        availableRoutes.removeAll()

        let transportTypes: [TransportType] = [.automobile, .walking, .transit]

        for transportType in transportTypes {
            calculateRoute(to: destination, transportType: transportType)
        }
    }

    // MARK: - 计算单个交通方式路线
    private func calculateRoute(to destination: CLLocationCoordinate2D, transportType: TransportType) {
        guard let userLocation = locationManager.userLocation else { return }

        // 特殊处理打车选项
        if transportType == .rideshare {
            // 打车不需要计算路线，直接创建一个估算的路线选项
            let distance = userLocation.distance(from: CLLocation(latitude: destination.latitude, longitude: destination.longitude))
            let estimatedTime = distance / 1000 * 3 * 60 // 假设平均速度30km/h

            let routeOption = RouteOption(
                route: MKRoute(), // 空路线
                transportType: transportType,
                duration: estimatedTime,
                distance: distance,
                description: "约 \(String(format: "%.1f", distance/1000)) km · \(Int(estimatedTime/60)) 分钟"
            )

            DispatchQueue.main.async {
                self.availableRoutes.append(routeOption)
                print("✅ \(transportType.rawValue)选项已添加")
            }
            return
        }

        let request = MKDirections.Request()
        request.source = MKMapItem(placemark: MKPlacemark(coordinate: userLocation.coordinate))
        request.destination = MKMapItem(placemark: MKPlacemark(coordinate: destination))
        request.transportType = transportType.mkTransportType

        // 为公交设置特殊选项
        if transportType == .transit {
            request.requestsAlternateRoutes = true
        }

        let directions = MKDirections(request: request)
        directions.calculate { response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ \(transportType.rawValue)路线计算错误: \(error.localizedDescription)")

                    // 如果公交路线不可用，提供备用信息
                    if transportType == .transit {
                        let fallbackOption = RouteOption(
                            route: MKRoute(),
                            transportType: transportType,
                            duration: 0,
                            distance: 0,
                            description: "该地区暂无公交路线"
                        )
                        self.availableRoutes.append(fallbackOption)
                    }
                    return
                }

                guard let route = response?.routes.first else {
                    print("❌ 无法找到\(transportType.rawValue)路线")

                    // 为骑车提供基于步行路线的估算
                    if transportType == .cycling {
                        self.calculateCyclingRouteFromWalking(to: destination)
                    }
                    return
                }

                let routeOption = RouteOption(
                    route: route,
                    transportType: transportType,
                    duration: transportType == .cycling ? route.expectedTravelTime * 0.4 : route.expectedTravelTime, // 骑车比步行快约2.5倍
                    distance: route.distance,
                    description: self.formatRouteDescription(route: route, transportType: transportType)
                )

                self.availableRoutes.append(routeOption)

                // 如果是当前选中的交通方式，更新显示的路线
                if transportType == self.selectedTransportType {
                    self.currentRoute = route

                    // 调整地图视角以显示整条路线
                    withAnimation(.easeInOut(duration: 1.5)) {
                        self.cameraPosition = .region(MKCoordinateRegion(route.polyline.boundingMapRect))
                    }
                }

                print("✅ \(transportType.rawValue)路线计算成功，距离: \(String(format: "%.1f", route.distance/1000)) km，时间: \(Int(routeOption.duration/60)) 分钟")
            }
        }
    }

    // MARK: - 基于步行路线计算骑车路线
    private func calculateCyclingRouteFromWalking(to destination: CLLocationCoordinate2D) {
        guard let userLocation = locationManager.userLocation else { return }

        let request = MKDirections.Request()
        request.source = MKMapItem(placemark: MKPlacemark(coordinate: userLocation.coordinate))
        request.destination = MKMapItem(placemark: MKPlacemark(coordinate: destination))
        request.transportType = .walking

        let directions = MKDirections(request: request)
        directions.calculate { response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ 骑车路线(基于步行)计算错误: \(error.localizedDescription)")
                    return
                }

                guard let route = response?.routes.first else {
                    print("❌ 无法找到骑车路线(基于步行)")
                    return
                }

                // 骑车比步行快约2.5倍
                let cyclingTime = route.expectedTravelTime * 0.4

                let routeOption = RouteOption(
                    route: route,
                    transportType: .cycling,
                    duration: cyclingTime,
                    distance: route.distance,
                    description: "\(String(format: "%.1f", route.distance/1000)) km · \(Int(cyclingTime/60)) 分钟"
                )

                self.availableRoutes.append(routeOption)

                // 如果当前选中骑车，更新显示
                if self.selectedTransportType == .cycling {
                    self.currentRoute = route
                    withAnimation(.easeInOut(duration: 1.5)) {
                        self.cameraPosition = .region(MKCoordinateRegion(route.polyline.boundingMapRect))
                    }
                }

                print("✅ 骑车路线(基于步行)计算成功，距离: \(String(format: "%.1f", route.distance/1000)) km，时间: \(Int(cyclingTime/60)) 分钟")
            }
        }
    }

    // MARK: - 格式化路线描述
    private func formatRouteDescription(route: MKRoute, transportType: TransportType) -> String {
        let distance = String(format: "%.1f", route.distance / 1000)
        let time: Int

        switch transportType {
        case .cycling:
            // 骑车时间已在RouteOption中计算
            time = Int(route.expectedTravelTime * 0.4 / 60)
        case .transit:
            // 公交时间可能包含等车时间
            time = Int(route.expectedTravelTime / 60)
        default:
            time = Int(route.expectedTravelTime / 60)
        }

        return "\(distance) km · \(time) 分钟"
    }

    // MARK: - 开始导航
    private func startNavigation() {
        guard let destination = selectedLocation, let route = currentRoute else {
            print("❌ 无法开始导航：缺少目的地或路线")
            return
        }

        isNavigating = true
        isNavigationMapMode = true
        currentStepIndex = 0
        navigationSteps = route.steps
        remainingDistance = route.distance
        remainingTime = route.expectedTravelTime

        // 设置初始导航指令
        updateNavigationInstructions()

        // 调整地图视角到导航模式
        adjustCameraForNavigation()

        // 开始导航更新定时器
        startNavigationTimer()

        // 开始航向监听
        locationManager.startUpdatingHeading()

        // 初始化角度平滑处理
        smoothedHeading = locationManager.deviceHeading
        lastHeading = locationManager.deviceHeading

        print("🧭 开始导航到: \(destinationName)")
        print("📍 总距离: \(String(format: "%.1f", remainingDistance/1000)) km")
        print("⏱️ 预计时间: \(Int(remainingTime/60)) 分钟")
    }

    // MARK: - 停止导航
    private func stopNavigation() {
        isNavigating = false
        isNavigationMapMode = false
        currentStepIndex = 0
        navigationSteps = []
        remainingDistance = 0
        remainingTime = 0
        currentInstruction = ""
        nextInstruction = ""

        // 清除路线和目的地信息
        currentRoute = nil
        selectedLocation = nil
        destinationName = ""
        availableRoutes.removeAll()

        // 停止导航定时器
        navigationTimer?.invalidate()
        navigationTimer = nil

        // 停止航向监听
        locationManager.stopUpdatingHeading()

        // 恢复正常地图视角
        if let userLocation = locationManager.userLocation {
            withAnimation(.easeInOut(duration: 1.0)) {
                cameraPosition = .region(MKCoordinateRegion(
                    center: userLocation.coordinate,
                    span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
                ))
            }
        }

        print("🛑 停止导航，已清除路线和目的地")
    }
    
    // MARK: - 定位到用户位置
    private func centerOnUserLocation() {
        if let userLocation = locationManager.userLocation {
            withAnimation(.easeInOut(duration: 1.0)) {
                cameraPosition = .region(MKCoordinateRegion(
                    center: userLocation.coordinate,
                    span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
                ))
            }
        }
    }

    // MARK: - 导航相关函数
    private func updateNavigationInstructions() {
        guard currentStepIndex < navigationSteps.count else { return }

        let currentStep = navigationSteps[currentStepIndex]
        currentInstruction = currentStep.instructions.isEmpty ? "继续前行" : currentStep.instructions

        // 设置下一步指令
        if currentStepIndex + 1 < navigationSteps.count {
            let nextStep = navigationSteps[currentStepIndex + 1]
            nextInstruction = nextStep.instructions.isEmpty ? "继续前行" : nextStep.instructions
        } else {
            nextInstruction = "即将到达目的地"
        }
    }

    private func adjustCameraForNavigation() {
        guard let userLocation = locationManager.userLocation else { return }

        withAnimation(.easeInOut(duration: 1.5)) {
            cameraPosition = .region(MKCoordinateRegion(
                center: userLocation.coordinate,
                span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005) // 更近的视角
            ))
        }
    }

    private func startNavigationTimer() {
        navigationTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            updateNavigationProgress()
        }
    }

    private func updateNavigationProgress() {
        guard let userLocation = locationManager.userLocation,
              let destination = selectedLocation else { return }

        // 计算到目的地的剩余距离
        let userCLLocation = CLLocation(latitude: userLocation.coordinate.latitude, longitude: userLocation.coordinate.longitude)
        let destinationCLLocation = CLLocation(latitude: destination.latitude, longitude: destination.longitude)
        remainingDistance = userCLLocation.distance(from: destinationCLLocation)

        // 估算剩余时间（基于当前交通方式）
        let speed: Double = {
            switch selectedTransportType {
            case .automobile: return 30.0 // 30 km/h 城市平均速度
            case .walking: return 5.0 // 5 km/h
            case .cycling: return 15.0 // 15 km/h
            case .transit: return 20.0 // 20 km/h
            case .rideshare: return 25.0 // 25 km/h
            }
        }()

        remainingTime = (remainingDistance / 1000) / speed * 3600 // 转换为秒

        // 检查是否需要更新当前步骤
        checkNavigationStepProgress(userLocation: userLocation)

        // 检查是否到达目的地
        if remainingDistance < 50 { // 50米内认为到达
            arriveAtDestination()
        }

        // 更新地图视角跟随用户位置
        if isNavigating {
            withAnimation(.easeInOut(duration: 0.5)) {
                cameraPosition = .region(MKCoordinateRegion(
                    center: userLocation.coordinate,
                    span: MKCoordinateSpan(latitudeDelta: 0.005, longitudeDelta: 0.005)
                ))
            }
        }
    }

    private func checkNavigationStepProgress(userLocation: CLLocation) {
        guard currentStepIndex < navigationSteps.count else { return }

        let currentStep = navigationSteps[currentStepIndex]
        let stepEndLocation = CLLocation(
            latitude: currentStep.polyline.coordinate.latitude,
            longitude: currentStep.polyline.coordinate.longitude
        )

        // 如果距离当前步骤终点很近，进入下一步
        if userLocation.distance(from: stepEndLocation) < 30 { // 30米内
            if currentStepIndex + 1 < navigationSteps.count {
                currentStepIndex += 1
                updateNavigationInstructions()
                print("📍 进入下一导航步骤: \(currentInstruction)")
            }
        }
    }

    private func arriveAtDestination() {
        print("🎉 已到达目的地: \(destinationName)")

        // 显示到达提示
        withAnimation(.spring()) {
            showingAIPopup = true
            aiPopupMessage = "🎉 恭喜！您已到达目的地：\(destinationName)"
        }

        // 自动停止导航
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            self.stopNavigation()
        }
    }
    

}

// MARK: - 地图搜索栏组件
struct MapSearchBarView: View {
    @Binding var searchText: String
    let onSearch: (String) -> Void
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.gray)
            
            TextField("搜索地点...", text: $searchText)
                .textFieldStyle(.plain)
                .onSubmit {
                    onSearch(searchText)
                }
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
}

// MARK: - 搜索结果列表
struct SearchResultsView: View {
    let searchResults: [MKMapItem]
    let onSelectResult: (MKMapItem) -> Void
    let onDismiss: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Text("搜索结果")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Button(action: onDismiss) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.gray)
                }
            }
            .padding()
            .background(Color(.systemBackground))

            // 结果列表
            ScrollView {
                LazyVStack(spacing: 0) {
                    ForEach(searchResults, id: \.self) { mapItem in
                        SearchResultRowView(mapItem: mapItem) {
                            onSelectResult(mapItem)
                        }
                        Divider()
                    }
                }
            }
            .frame(maxHeight: 200)
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        .padding(.horizontal)
    }
}

// MARK: - 搜索结果行
struct SearchResultRowView: View {
    let mapItem: MKMapItem
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 12) {
                Image(systemName: "mappin.circle.fill")
                    .foregroundColor(.red)
                    .font(.title3)

                VStack(alignment: .leading, spacing: 4) {
                    Text(mapItem.name ?? "未知地点")
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    if let address = formatAddress(mapItem.placemark) {
                        Text(address)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                    }
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            .padding()
        }
        .buttonStyle(.plain)
    }

    private func formatAddress(_ placemark: CLPlacemark) -> String? {
        var components: [String] = []

        if let thoroughfare = placemark.thoroughfare {
            components.append(thoroughfare)
        }
        if let locality = placemark.locality {
            components.append(locality)
        }
        if let administrativeArea = placemark.administrativeArea {
            components.append(administrativeArea)
        }

        return components.isEmpty ? nil : components.joined(separator: ", ")
    }
}

// MARK: - 底部控制栏
struct BottomControlsView: View {
    let isNavigating: Bool
    let hasDestination: Bool
    let selectedTransportType: MapNavigationView.TransportType
    let availableRoutes: [MapNavigationView.RouteOption]
    let onLocationCenter: () -> Void
    let onStartNavigation: () -> Void
    let onStopNavigation: () -> Void
    let onTransportTypeChanged: (MapNavigationView.TransportType) -> Void
    let onShowRideOptions: () -> Void

    @State private var showingTransportOptions = false

    var body: some View {
        VStack(spacing: 8) {
            // 交通方式选择栏（当有目的地时显示）
            if hasDestination {
                TransportTypeSelector(
                    selectedType: selectedTransportType,
                    availableRoutes: availableRoutes,
                    onTypeChanged: onTransportTypeChanged,
                    onShowRideOptions: onShowRideOptions
                )
            }

            // 主控制栏
            HStack(spacing: 12) {
                // 定位按钮
                Button(action: onLocationCenter) {
                    Image(systemName: "location.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                        .frame(width: 44, height: 44)
                        .background(Color(.systemBackground))
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                }

                // 导航控制按钮
                if hasDestination {
                    if isNavigating {
                        Button(action: onStopNavigation) {
                            HStack {
                                Image(systemName: "stop.circle.fill")
                                Text("停止导航")
                            }
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(Color.red)
                            .cornerRadius(20)
                        }
                    } else {
                        Button(action: onStartNavigation) {
                            HStack {
                                Image(systemName: "location.north.circle.fill")
                                Text("开始导航")
                            }
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(Color.green)
                            .cornerRadius(20)
                        }
                    }
                }

                Spacer()
            }
        }
    }
}

// MARK: - 交通方式选择器
struct TransportTypeSelector: View {
    let selectedType: MapNavigationView.TransportType
    let availableRoutes: [MapNavigationView.RouteOption]
    let onTypeChanged: (MapNavigationView.TransportType) -> Void
    let onShowRideOptions: () -> Void

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(MapNavigationView.TransportType.allCases, id: \.self) { transportType in
                    TransportOptionButton(
                        transportType: transportType,
                        isSelected: selectedType == transportType,
                        routeInfo: getRouteInfo(for: transportType),
                        onTap: {
                            if transportType == .rideshare {
                                onShowRideOptions()
                            } else {
                                onTypeChanged(transportType)
                            }
                        }
                    )
                }
            }
            .padding(.horizontal)
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    private func getRouteInfo(for transportType: MapNavigationView.TransportType) -> String? {
        guard let route = availableRoutes.first(where: { $0.transportType == transportType }) else {
            // 如果没有路线信息，显示状态提示
            switch transportType {
            case .transit:
                return "计算中..."
            case .cycling:
                return "计算中..."
            case .rideshare:
                return "多种选择"
            default:
                return "计算中..."
            }
        }

        // 特殊处理无效路线
        if route.description.contains("暂无") {
            return route.description
        }

        return route.description
    }
}

// MARK: - 交通方式选项按钮
struct TransportOptionButton: View {
    let transportType: MapNavigationView.TransportType
    let isSelected: Bool
    let routeInfo: String?
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                Image(systemName: transportType.icon)
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : .primary)

                Text(transportType.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)

                if let routeInfo = routeInfo {
                    Text(routeInfo)
                        .font(.caption2)
                        .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
                        .lineLimit(1)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue : Color.clear)
            )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - AI消息横幅组件
struct AIMessageBannerView: View {
    let message: String
    let onDismiss: () -> Void
    @State private var dragOffset: CGFloat = 0

    var body: some View {
        VStack {
            HStack(spacing: 12) {
                // AI头像
                Image(systemName: "heart.circle.fill")
                    .font(.title3)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.orange, .pink],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )

                // 消息内容
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                Spacer()

                // 关闭按钮
                Button(action: onDismiss) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title3)
                        .foregroundColor(.gray)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            )
            .offset(y: dragOffset)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        // 只允许向上滑动
                        if value.translation.height < 0 {
                            dragOffset = value.translation.height
                        }
                    }
                    .onEnded { value in
                        // 如果向上滑动超过50点，则关闭弹窗
                        if value.translation.height < -50 {
                            onDismiss()
                        } else {
                            // 否则回弹
                            withAnimation(.spring()) {
                                dragOffset = 0
                            }
                        }
                    }
            )
            .padding(.horizontal, 16)
            .padding(.top, 8)

            Spacer()
        }
    }
}



// MARK: - 打车选项弹窗
struct RideOptionsView: View {
    let destination: CLLocationCoordinate2D?
    let destinationName: String
    @Environment(\.dismiss) private var dismiss

    private let rideOptions = [
        RideOption(name: "滴滴出行", icon: "car.fill", color: .orange, urlScheme: "diditaxi://"),
        RideOption(name: "高德地图", icon: "map.fill", color: .blue, urlScheme: "iosamap://"),
        RideOption(name: "百度地图", icon: "location.fill", color: .red, urlScheme: "baidumap://"),
        RideOption(name: "腾讯地图", icon: "mappin.circle.fill", color: .green, urlScheme: "qqmap://"),
        RideOption(name: "Apple地图", icon: "map", color: .gray, urlScheme: "maps://")
    ]

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题信息
                VStack(spacing: 8) {
                    Image(systemName: "car.2.fill")
                        .font(.largeTitle)
                        .foregroundColor(.blue)

                    Text("选择打车应用")
                        .font(.title2)
                        .fontWeight(.bold)

                    if !destinationName.isEmpty {
                        Text("前往：\(destinationName)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.top)

                // 应用选项列表
                LazyVStack(spacing: 12) {
                    ForEach(rideOptions, id: \.name) { option in
                        RideOptionRow(option: option) {
                            openRideApp(option)
                        }
                    }
                }
                .padding(.horizontal)

                Spacer()
            }
            .navigationTitle("打车服务")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func openRideApp(_ option: RideOption) {
        guard let destination = destination else { return }

        let lat = destination.latitude
        let lng = destination.longitude
        let name = destinationName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""

        var urlString = ""
        var testUrlString = "" // 用于检测应用是否安装的简单URL

        switch option.name {
        case "滴滴出行":
            testUrlString = "diditaxi://"
            urlString = "diditaxi://one_key_call?start_lat=&start_lng=&end_lat=\(lat)&end_lng=\(lng)&end_name=\(name)"
        case "高德地图":
            testUrlString = "iosamap://"
            // 修正高德地图的URL Scheme，使用正确的导航参数
            urlString = "iosamap://navi?sourceApplication=travel_with&poiname=\(name)&poiid=BGVIS&lat=\(lat)&lon=\(lng)&dev=0&style=2"
        case "百度地图":
            testUrlString = "baidumap://"
            urlString = "baidumap://map/direction?destination=latlng:\(lat),\(lng)|name:\(name)&mode=driving&src=travel_with"
        case "腾讯地图":
            testUrlString = "qqmap://"
            urlString = "qqmap://map/routeplan?type=drive&to=\(name)&tocoord=\(lat),\(lng)&from=我的位置&referer=travel_with"
        case "Apple地图":
            testUrlString = "maps://"
            urlString = "maps://?daddr=\(lat),\(lng)&dirflg=d" // 添加驾车导航标志
        default:
            break
        }

        // 首先检测应用是否安装
        if let testUrl = URL(string: testUrlString), UIApplication.shared.canOpenURL(testUrl) {
            // 应用已安装，打开具体功能
            if let url = URL(string: urlString) {
                UIApplication.shared.open(url) { success in
                    if success {
                        dismiss()
                    } else {
                        // 如果具体功能URL失败，尝试打开应用主页
                        UIApplication.shared.open(testUrl) { success in
                            if success {
                                dismiss()
                            }
                        }
                    }
                }
            }
        } else {
            // 应用未安装，打开App Store
            print("应用 \(option.name) 未安装，跳转到App Store")
            openAppStore(for: option)
        }
    }

    private func openAppStore(for option: RideOption) {
        let appStoreURLs = [
            "滴滴出行": "https://apps.apple.com/cn/app/滴滴出行/id554499054",
            "高德地图": "https://apps.apple.com/cn/app/高德地图/id461703208",
            "百度地图": "https://apps.apple.com/cn/app/百度地图/id452186370",
            "腾讯地图": "https://apps.apple.com/cn/app/腾讯地图/id481623196"
        ]

        if let urlString = appStoreURLs[option.name],
           let url = URL(string: urlString) {
            UIApplication.shared.open(url)
        }
    }
}

// MARK: - 打车选项数据模型
struct RideOption {
    let name: String
    let icon: String
    let color: Color
    let urlScheme: String
}

// MARK: - 打车选项行
struct RideOptionRow: View {
    let option: RideOption
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 应用图标
                Image(systemName: option.icon)
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(option.color)
                    .clipShape(RoundedRectangle(cornerRadius: 10))

                // 应用信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(option.name)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text("点击打开应用")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 箭头图标
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 导航信息面板
struct NavigationInfoPanel: View {
    let currentInstruction: String
    let nextInstruction: String
    let remainingDistance: CLLocationDistance
    let remainingTime: TimeInterval
    let onStopNavigation: () -> Void

    var body: some View {
        VStack(spacing: 12) {
            // 顶部控制栏
            HStack {
                Button(action: onStopNavigation) {
                    HStack(spacing: 6) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title3)
                        Text("结束")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.red)
                }

                Spacer()

                // 剩余时间和距离
                VStack(alignment: .trailing, spacing: 2) {
                    Text(formatTime(remainingTime))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text(formatDistance(remainingDistance))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // 当前指令
            HStack(spacing: 12) {
                Image(systemName: "arrow.turn.up.right")
                    .font(.title)
                    .foregroundColor(.blue)
                    .frame(width: 30)

                VStack(alignment: .leading, spacing: 4) {
                    Text(currentInstruction)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)

                    if !nextInstruction.isEmpty {
                        Text("然后：\(nextInstruction)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                    }
                }

                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval / 60)
        if minutes < 60 {
            return "\(minutes) 分钟"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            return "\(hours) 小时 \(remainingMinutes) 分钟"
        }
    }

    private func formatDistance(_ distance: CLLocationDistance) -> String {
        if distance < 1000 {
            return "\(Int(distance)) 米"
        } else {
            return String(format: "%.1f 公里", distance / 1000)
        }
    }
}

// MARK: - 计划选择器视图
struct PlanSelectorView: View {
    @ObservedObject var placeManager: PlaceManager
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 当前选中计划信息
                if let selectedPlan = placeManager.selectedPlan {
                    VStack(spacing: 8) {
                        HStack {
                            Image(systemName: "map.circle.fill")
                                .font(.title2)
                                .foregroundColor(.blue)

                            VStack(alignment: .leading, spacing: 2) {
                                Text("当前显示")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text(selectedPlan.name)
                                    .font(.headline)
                                    .fontWeight(.semibold)
                            }

                            Spacer()

                            Text("\(selectedPlan.places.count)")
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.blue)
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                        .padding(.horizontal)
                        .padding(.top, 8)
                    }
                }

                // 计划列表
                List {
                    Section("所有计划") {
                        ForEach(placeManager.travelPlans, id: \.id) { plan in
                            PlanRowView(
                                plan: plan,
                                isSelected: placeManager.selectedPlan?.id == plan.id,
                                onSelect: {
                                    placeManager.selectedPlan = plan
                                    dismiss()
                                }
                            )
                        }
                    }
                }
                .listStyle(.insetGrouped)
            }
            .navigationTitle("地图显示计划")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 计划行视图
struct PlanRowView: View {
    let plan: TravelPlan
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 12) {
                // 计划图标
                Image(systemName: plan.name == "全部地点" ? "location.circle.fill" : "map.fill")
                    .font(.title3)
                    .foregroundColor(isSelected ? .blue : .secondary)
                    .frame(width: 24, height: 24)

                VStack(alignment: .leading, spacing: 4) {
                    Text(plan.name)
                        .font(.body)
                        .fontWeight(isSelected ? .semibold : .medium)
                        .foregroundColor(.primary)

                    Text("\(plan.places.count) 个地点")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 选中状态指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title3)
                } else {
                    Image(systemName: "circle")
                        .foregroundColor(.secondary)
                        .font(.title3)
                }
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 预览
#Preview {
    MapNavigationView()
        .environmentObject(AIService.preview)
        .environmentObject(PlaceManager.preview)
}
