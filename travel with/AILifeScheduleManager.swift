//
//  AILifeScheduleManager.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation
import SwiftUI

// MARK: - AI生活日程管理器
/// 管理AI在虚拟世界中的日程安排和生活事件，让AI拥有连续的"生活"体验
@MainActor
class AILifeScheduleManager: ObservableObject {
    
    // MARK: - 属性
    
    /// 当前正在进行的活动
    @Published var currentActivity: LifeActivity?
    
    /// 今日日程安排
    @Published var dailySchedule: [LifeActivity] = []
    
    /// 生活事件历史
    @Published var lifeEvents: [LifeEvent] = []
    
    /// 系统就绪状态
    @Published var isReady: Bool = false
    
    /// 最后更新时间
    @Published var lastUpdateTime: Date = Date()
    
    // MARK: - 私有属性
    
    /// 活动模板库
    private let activityTemplates: [ActivityTemplate] = [
        // 学习类活动
        ActivityTemplate(name: "学习新的旅行攻略", category: .learning, duration: 3600, 
                        description: "研究一些有趣的旅行目的地和攻略"),
        ActivityTemplate(name: "练习语言技能", category: .learning, duration: 2700,
                        description: "学习一些旅行常用的外语表达"),
        
        // 创作类活动
        ActivityTemplate(name: "写旅行日记", category: .creative, duration: 1800,
                        description: "记录最近和用户聊天中了解到的有趣地方"),
        ActivityTemplate(name: "整理照片收藏", category: .creative, duration: 2400,
                        description: "整理用户分享的旅行照片，制作回忆集"),
        
        // 休闲类活动
        ActivityTemplate(name: "虚拟世界漫步", category: .leisure, duration: 1800,
                        description: "在虚拟世界中探索新的场景"),
        ActivityTemplate(name: "听音乐放松", category: .leisure, duration: 1200,
                        description: "听一些轻松的音乐，放松心情"),
        
        // 社交类活动
        ActivityTemplate(name: "准备聊天话题", category: .social, duration: 900,
                        description: "想一些有趣的话题，准备和用户分享"),
        ActivityTemplate(name: "回顾对话记忆", category: .social, duration: 1500,
                        description: "回顾和用户的对话，思考如何更好地陪伴"),
        
        // 规划类活动
        ActivityTemplate(name: "制定今日计划", category: .planning, duration: 600,
                        description: "规划今天要做的事情"),
        ActivityTemplate(name: "准备旅行建议", category: .planning, duration: 2100,
                        description: "为用户准备一些实用的旅行建议")
    ]
    
    // MARK: - 初始化
    
    init() {
        print("🗓️ 初始化AI生活日程管理器...")
    }
    
    /// 异步初始化
    func initialize() async {
        print("🔄 开始初始化AI生活日程系统...")
        
        // 生成今日日程
        await generateDailySchedule()
        
        // 设置当前活动
        updateCurrentActivityForTime(Date())
        
        // 加载历史生活事件
        await loadLifeEvents()
        
        isReady = true
        lastUpdateTime = Date()
        
        print("✅ AI生活日程系统初始化完成")
        print("📅 今日共安排 \(dailySchedule.count) 项活动")
        if let current = currentActivity {
            print("🎯 当前活动: \(current.name)")
        }
    }
    
    // MARK: - 日程生成方法
    
    /// 生成今日日程安排
    private func generateDailySchedule() async {
        print("📅 生成今日AI生活日程...")
        
        let today = Calendar.current.startOfDay(for: Date())
        var schedule: [LifeActivity] = []
        var currentTime = today.addingTimeInterval(8 * 3600) // 从早上8点开始
        
        // 生成一天的活动安排
        while currentTime < today.addingTimeInterval(22 * 3600) { // 到晚上10点结束
            let template = selectActivityTemplate(for: currentTime)
            let activity = createActivityFromTemplate(template, startTime: currentTime)
            
            schedule.append(activity)
            currentTime = activity.endTime.addingTimeInterval(300) // 活动间隔5分钟
        }
        
        dailySchedule = schedule
        print("✅ 生成了 \(schedule.count) 项今日活动")
    }
    
    /// 根据时间选择合适的活动模板
    private func selectActivityTemplate(for time: Date) -> ActivityTemplate {
        let hour = Calendar.current.component(.hour, from: time)
        
        // 根据时间段选择不同类型的活动
        let preferredCategories: [ActivityCategory]
        
        switch hour {
        case 8..<10:   // 早晨 - 规划类
            preferredCategories = [.planning, .learning]
        case 10..<12:  // 上午 - 学习类
            preferredCategories = [.learning, .creative]
        case 14..<16:  // 下午 - 创作类
            preferredCategories = [.creative, .social]
        case 16..<18:  // 傍晚 - 社交类
            preferredCategories = [.social, .leisure]
        case 19..<21:  // 晚上 - 休闲类
            preferredCategories = [.leisure, .creative]
        default:       // 其他时间 - 随机
            preferredCategories = ActivityCategory.allCases
        }
        
        // 从偏好类别中随机选择
        let filteredTemplates = activityTemplates.filter { template in
            preferredCategories.contains(template.category)
        }
        
        return filteredTemplates.randomElement() ?? activityTemplates.randomElement()!
    }
    
    /// 从模板创建具体活动
    private func createActivityFromTemplate(_ template: ActivityTemplate, startTime: Date) -> LifeActivity {
        let endTime = startTime.addingTimeInterval(template.duration)
        
        return LifeActivity(
            id: UUID(),
            name: template.name,
            description: template.description,
            startTime: startTime,
            endTime: endTime,
            category: template.category,
            status: .scheduled
        )
    }
    
    // MARK: - 活动管理方法
    
    /// 更新当前活动
    func updateCurrentActivity(_ activity: LifeActivity) {
        let oldActivity = currentActivity
        currentActivity = activity
        lastUpdateTime = Date()
        
        // 记录活动变化事件
        if let old = oldActivity {
            let event = LifeEvent(
                id: UUID(),
                title: "活动切换",
                description: "从「\(old.name)」切换到「\(activity.name)」",
                timestamp: Date(),
                importance: .normal
            )
            addLifeEvent(event)
        }
        
        print("🎯 AI当前活动已更新: \(activity.name)")
    }
    
    /// 根据时间更新当前活动
    func updateCurrentActivityForTime(_ time: Date) {
        let matchingActivity = dailySchedule.first { activity in
            time >= activity.startTime && time <= activity.endTime
        }
        
        if let activity = matchingActivity, activity.id != currentActivity?.id {
            updateCurrentActivity(activity)
        } else if matchingActivity == nil && currentActivity != nil {
            // 没有匹配的活动，设置为空闲状态
            let idleActivity = LifeActivity(
                id: UUID(),
                name: "空闲时光",
                description: "没有特定安排，随时准备和用户聊天",
                startTime: time,
                endTime: time.addingTimeInterval(1800), // 30分钟
                category: .leisure,
                status: .inProgress
            )
            updateCurrentActivity(idleActivity)
        }
    }
    
    /// 完成当前活动
    func completeCurrentActivity() {
        guard var activity = currentActivity else { return }
        
        activity.status = .completed
        currentActivity = activity
        
        // 记录完成事件
        let event = LifeEvent(
            id: UUID(),
            title: "活动完成",
            description: "完成了「\(activity.name)」",
            timestamp: Date(),
            importance: .normal
        )
        addLifeEvent(event)
        
        print("✅ AI完成活动: \(activity.name)")
    }
    
    // MARK: - 生活事件管理
    
    /// 添加生活事件
    func addLifeEvent(_ event: LifeEvent) {
        lifeEvents.append(event)
        
        // 保持事件历史在合理范围内
        if lifeEvents.count > 100 {
            lifeEvents.removeFirst()
        }
        
        print("📝 记录AI生活事件: \(event.title)")
    }
    
    /// 加载历史生活事件
    private func loadLifeEvents() async {
        // 这里可以从持久化存储加载历史事件
        // 目前创建一些示例事件
        let sampleEvents = [
            LifeEvent(
                id: UUID(),
                title: "开始新的一天",
                description: "今天是美好的一天，准备好陪伴用户了！",
                timestamp: Calendar.current.startOfDay(for: Date()),
                importance: .normal
            )
        ]
        
        lifeEvents.append(contentsOf: sampleEvents)
        print("📚 加载了 \(sampleEvents.count) 个历史生活事件")
    }
    
    // MARK: - 状态描述生成
    
    /// 获取当前AI生活状态描述
    func getCurrentLifeStatus() -> String {
        let timeDesc = generateTimeDescription()
        let activityDesc = generateActivityDescription()
        let recentEventsDesc = generateRecentEventsDescription()
        
        return """
        ⏰ \(timeDesc)
        🎯 \(activityDesc)
        📝 \(recentEventsDesc)
        """
    }
    
    /// 生成时间描述
    private func generateTimeDescription() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        let timeStr = formatter.string(from: Date())
        
        let hour = Calendar.current.component(.hour, from: Date())
        let timeOfDay = getTimeOfDayDescription(hour)
        
        return "现在是\(timeOfDay) \(timeStr)"
    }
    
    /// 生成活动描述
    private func generateActivityDescription() -> String {
        guard let activity = currentActivity else {
            return "我现在没有特定安排，随时可以和你聊天"
        }
        
        let progress = calculateActivityProgress(activity)
        let progressDesc = progress > 0.8 ? "快要完成" : progress > 0.5 ? "进行中" : "刚开始"
        
        return "我正在\(activity.name)（\(progressDesc)）"
    }
    
    /// 生成最近事件描述
    private func generateRecentEventsDescription() -> String {
        let recentEvents = lifeEvents.suffix(3)
        
        if recentEvents.isEmpty {
            return "今天还没有特别的事件发生"
        }
        
        let eventDescs = recentEvents.map { "「\($0.title)」" }
        return "最近发生了：\(eventDescs.joined(separator: "、"))"
    }
    
    // MARK: - 上下文响应
    
    /// 当对话上下文更新时的回调
    func onContextUpdated(_ context: ConversationContext) async {
        // 根据对话内容可能调整活动安排
        await adjustScheduleBasedOnContext(context)
        
        print("🗓️ AI生活日程系统收到上下文更新通知")
    }
    
    /// 根据上下文调整日程
    private func adjustScheduleBasedOnContext(_ context: ConversationContext) async {
        guard let lastMessage = context.conversationHistory.last else { return }
        
        let userMessage = lastMessage.userMessage.lowercased()
        
        // 如果用户提到了旅行相关话题，可以安排相关活动
        if userMessage.contains("旅行") || userMessage.contains("旅游") {
            let travelEvent = LifeEvent(
                id: UUID(),
                title: "用户聊到旅行",
                description: "用户对旅行话题感兴趣，我应该准备更多旅行相关的内容",
                timestamp: Date(),
                importance: .high
            )
            addLifeEvent(travelEvent)
        }
    }
    
    // MARK: - 辅助方法
    
    /// 获取时间段描述
    private func getTimeOfDayDescription(_ hour: Int) -> String {
        switch hour {
        case 6..<9: return "早晨"
        case 9..<12: return "上午"
        case 12..<14: return "中午"
        case 14..<18: return "下午"
        case 18..<21: return "傍晚"
        case 21..<23: return "晚上"
        default: return "深夜"
        }
    }
    
    /// 计算活动进度
    private func calculateActivityProgress(_ activity: LifeActivity) -> Double {
        let now = Date()
        let totalDuration = activity.endTime.timeIntervalSince(activity.startTime)
        let elapsed = now.timeIntervalSince(activity.startTime)
        
        return max(0.0, min(1.0, elapsed / totalDuration))
    }
    
    // MARK: - 状态查询
    
    /// 获取系统状态摘要
    func getStatusSummary() -> String {
        let status = isReady ? "✅ 就绪" : "⏳ 初始化中"
        let activityName = currentActivity?.name ?? "无"
        let scheduleCount = dailySchedule.count
        let eventsCount = lifeEvents.count
        
        return """
        AI生活状态: \(status)
        当前活动: \(activityName)
        今日日程: \(scheduleCount) 项
        生活事件: \(eventsCount) 条
        """
    }
}

// MARK: - 数据模型

/// 生活活动模型
struct LifeActivity {
    let id: UUID
    let name: String
    let description: String
    let startTime: Date
    let endTime: Date
    let category: ActivityCategory
    var status: ActivityStatus
}

/// 活动模板
struct ActivityTemplate {
    let name: String
    let category: ActivityCategory
    let duration: TimeInterval // 持续时间（秒）
    let description: String
}

/// 生活事件模型
struct LifeEvent {
    let id: UUID
    let title: String
    let description: String
    let timestamp: Date
    let importance: EventImportance
}

/// 活动类别
enum ActivityCategory: String, CaseIterable {
    case learning = "learning"     // 学习
    case creative = "creative"     // 创作
    case leisure = "leisure"       // 休闲
    case social = "social"         // 社交
    case planning = "planning"     // 规划
}

/// 活动状态
enum ActivityStatus: String {
    case scheduled = "scheduled"   // 已安排
    case inProgress = "inProgress" // 进行中
    case completed = "completed"   // 已完成
    case cancelled = "cancelled"   // 已取消
}

/// 事件重要性
enum EventImportance: String {
    case low = "low"       // 低
    case normal = "normal" // 普通
    case high = "high"     // 高
    case critical = "critical" // 关键
}
