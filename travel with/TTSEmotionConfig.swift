//
//  TTSEmotionConfig.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

/// TTS情感配置管理器
/// 用于管理不同TTS音色支持的情感类型，方便后续修改和扩展
class TTSEmotionConfig {
    
    /// 当前使用的TTS音色类型
    enum TTSVoiceType: String, CaseIterable {
        case cancan = "zh_female_cancan_mars_bigtts"           // 灿灿2.0
        case wanwan = "zh_female_wanwanxiaohe_moon_bigtts"     // 湾湾小何
        case other = "other_voice"                              // 其他音色
    }
    
    /// 当前使用的音色类型
    static let currentVoiceType: TTSVoiceType = .cancan
    
    /// 获取当前音色支持的所有情感类型
    static func getSupportedEmotions() -> [CanCanEmotion] {
        switch currentVoiceType {
        case .cancan:
            return getCanCanSupportedEmotions()
        case .wanwan:
            return getWanWanSupportedEmotions()
        case .other:
            return getDefaultSupportedEmotions()
        }
    }
    
    /// 获取当前音色支持的情感类型（字符串数组，用于AI分析）
    static func getSupportedEmotionStrings() -> [String] {
        return getSupportedEmotions().map { "\($0.rawValue) - \($0.description)" }
    }
    
    /// 根据情感描述获取最匹配的TTS情感
    static func getMatchingEmotion(for description: String) -> CanCanEmotion {
        let supportedEmotions = getSupportedEmotions()
        let lowercaseDesc = description.lowercased()
        
        // 情感关键词映射
        let emotionKeywords: [String: CanCanEmotion] = [
            "开心": .happy,
            "快乐": .happy,
            "兴奋": .happy,
            "愉悦": .pleased,
            "满足": .pleased,
            "温柔": .pleased,
            "撒娇": .loveyDovey,
            "可爱": .loveyDovey,
            "甜美": .loveyDovey,
            "安慰": .comfort,
            "关怀": .comfort,
            "温暖": .comfort,
            "专业": .professional,
            "理性": .professional,
            "分析": .professional,
            "抱歉": .sorry,
            "道歉": .sorry,
            "傲娇": .tsundere,
            "小脾气": .tsundere,
            "俏皮": .charming,
            "调皮": .charming,
            "惊讶": .surprise,
            "意外": .surprise,
            "悲伤": .sad,
            "难过": .sad,
            "失落": .sad
        ]
        
        // 查找匹配的关键词
        for (keyword, emotion) in emotionKeywords {
            if lowercaseDesc.contains(keyword) && supportedEmotions.contains(emotion) {
                return emotion
            }
        }
        
        // 默认返回愉悦
        return .pleased
    }
    
    // MARK: - 不同音色支持的情感类型
    
    /// 灿灿2.0支持的情感类型
    private static func getCanCanSupportedEmotions() -> [CanCanEmotion] {
        return [
            .pleased,           // 通用/愉悦 (默认)
            .sorry,            // 抱歉
            .annoyed,          // 嗔怪
            .customerService,  // 客服
            .professional,     // 专业
            .serious,          // 严肃
            .happy,            // 开心
            .sad,              // 悲伤
            .angry,            // 愤怒
            .scare,            // 害怕
            .hate,             // 厌恶
            .surprise,         // 惊讶
            .tear,             // 哭腔
            .conniving,        // 绿茶
            .comfort,          // 安慰鼓励
            .radio,            // 情感电台
            .loveyDovey,       // 撒娇
            .tsundere,         // 傲娇
            .charming,         // 娇媚
            .yoga,             // 瑜伽
            .storytelling      // 讲故事
        ]
    }
    
    /// 湾湾小何支持的情感类型（示例，可根据实际情况调整）
    private static func getWanWanSupportedEmotions() -> [CanCanEmotion] {
        return [
            .pleased,
            .happy,
            .comfort,
            .loveyDovey,
            .charming,
            .surprise
        ]
    }
    
    /// 默认支持的情感类型
    private static func getDefaultSupportedEmotions() -> [CanCanEmotion] {
        return [
            .pleased,
            .happy,
            .comfort,
            .professional
        ]
    }
    
    // MARK: - 配置信息
    
    /// 获取当前TTS配置信息
    static func getCurrentConfig() -> String {
        return """
        当前TTS配置：
        - 音色类型：\(currentVoiceType.rawValue)
        - 支持情感数量：\(getSupportedEmotions().count)个
        - 支持的情感类型：\(getSupportedEmotions().map { $0.description }.joined(separator: "、"))
        """
    }
    
    /// 获取配置文件位置信息
    static func getConfigFileInfo() -> String {
        return """
        📁 TTS情感配置文件位置：travel with/TTSEmotionConfig.swift
        
        🔧 修改方法：
        1. 修改 currentVoiceType 来切换音色
        2. 修改对应的 getSupportedEmotions 方法来调整支持的情感
        3. 修改 emotionKeywords 来调整情感关键词映射
        
        💡 使用方法：
        - TTSEmotionConfig.getSupportedEmotions() // 获取支持的情感
        - TTSEmotionConfig.getMatchingEmotion(for: "开心") // 获取匹配的情感
        """
    }
}

// MARK: - 任务分配专用的情感配置

/// 任务分配智能体专用的情感配置
struct TaskAssignmentEmotionConfig {
    
    /// 获取用于任务分配的情感选项字符串
    static func getEmotionOptionsForTaskAssignment() -> String {
        let emotions = TTSEmotionConfig.getSupportedEmotions()
        let emotionList = emotions.map { "\"" + $0.rawValue + "\"" }.joined(separator: ", ")
        
        return """
        支持的TTS情感类型（必须从中选择一个）：
        [\(emotionList)]
        
        情感选择指南：
        - pleased: 日常愉悦、温柔对话
        - happy: 开心、兴奋的情况
        - comfort: 安慰、关怀用户时
        - loveyDovey: 撒娇、甜蜜的时候
        - professional: 理性分析、专业回答
        - sorry: 道歉或表示遗憾
        - surprise: 惊讶、意外的情况
        """
    }
}
