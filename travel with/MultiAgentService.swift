//
//  MultiAgentService.swift (重构为AgentScheduler)
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//  重构说明：将原有的简单多智能体服务重构为完整的智能体调度器
//

import Foundation
import UIKit

// MARK: - Agent Definition

/// 定义了每个智能体的唯一标识和对应的模型信息。
enum AgentIdentifier: String, CaseIterable {
    case taskAssignment = "ep-20250802140847-xmhjp"
    case easyCommunication = "ep-20250802204656-fnm4c"
    case thinkCommunication = "ep-20250802141138-5dfvd"
    case see = "ep-20250802141347-9wn2p"
    case emotionPerception = "ep-20250802141822-n59fx"

    var endpointName: String {
        switch self {
        case .taskAssignment: return "travel with Task assignment"
        case .easyCommunication: return "travel with easy communication"
        case .thinkCommunication: return "travel with think communication"
        case .see: return "travel with see"
        case .emotionPerception: return "travel with emotion perception"
        }
    }
}

// MARK: - Multi-Agent Service

@MainActor
class MultiAgentService {
    
    private let apiKey = "5bd19fd1-fee3-4290-b8d8-f21fcadeaf51"
    private let baseURL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    private let urlSession: URLSession

    init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 60
        config.timeoutIntervalForResource = 120
        self.urlSession = URLSession(configuration: config)
    }

    /// 智能体系统的主要入口点。
    /// 它接收用户输入，通过调度器确定意图，然后路由到最合适的智能体。
    func getResponse(for messages: [ChatMessage], image: Data? = nil) async -> ChatMessage {
        // 1. 确定用户意图
        let userIntent = await determineUserIntent(from: messages.last?.content ?? "你好")

        // 2. 根据意图选择并调用相应的智能体
        let agentToUse: AgentIdentifier
        switch userIntent {
        case .simpleChat:
            agentToUse = .easyCommunication
        case .deepChat:
            agentToUse = .thinkCommunication
        case .imageQuery:
            agentToUse = .see
        // 默认使用简单聊天
        default:
            agentToUse = .easyCommunication
        }
        
        // 如果有图片，强制使用多模态智能体
        let finalAgent = (image != nil) ? .see : agentToUse

        // 3. 准备API请求的消息体
        let apiMessages = await prepareAPIMessages(for: finalAgent, userMessages: messages, image: image)

        // 4. 调用API
        let responseText = await callAPI(with: finalAgent, messages: apiMessages)

        // 5. 格式化并返回响应
        return ChatMessage(content: responseText, isFromUser: false)
    }

    /// 调用任务分配智能体来确定用户意图。
    private func determineUserIntent(from userInput: String) async -> UserIntent {
        let systemPrompt = """
        你是一个任务分类助手。你需要根据用户的输入，判断其意图属于以下哪一类：
        - "simple_chat": 简单的日常问候、闲聊、事实问答。例如："你好"、"今天天气怎么样"、"法国的首都是哪里"。
        - "deep_chat": 需要深入思考、情感交流、创意写作或复杂推理的对话。例如："我最近感觉很难过"、"你觉得人生的意义是什么"、"帮我写一首关于夏天的诗"。
        - "image_query": 当用户输入明显是关于一张图片时。例如："这张图里有什么？"、"这是什么地方？"。
        请只返回分类标签（"simple_chat", "deep_chat", "image_query"），不要包含任何其他解释。
        """
        let messages = [
            APIRequest.Message(role: "system", content: .string(systemPrompt)),
            APIRequest.Message(role: "user", content: .string(userInput))
        ]
        
        let response = await callAPI(with: .taskAssignment, messages: messages)
        
        switch response.trimmingCharacters(in: .whitespacesAndNewlines) {
        case "simple_chat":
            return .simpleChat
        case "deep_chat":
            return .deepChat
        case "image_query":
            return .imageQuery
        default:
            return .simpleChat // 默认为简单聊天
        }
    }
    
    /// 准备用于API请求的消息数组。
    private func prepareAPIMessages(for agent: AgentIdentifier, userMessages: [ChatMessage], image: Data?) async -> [APIRequest.Message] {
        var apiMessages: [APIRequest.Message] = []

        // 系统提示词
        let systemPrompt = "你是我的好朋友，我们经常一起聊天。用朋友之间日常聊天的语气回复，要简短自然，就像微信聊天一样。不要太正式或者太长，偶尔可以用点表情符号。"
        apiMessages.append(APIRequest.Message(role: "system", content: .string(systemPrompt)))

        // 构建上下文
        for message in userMessages.suffix(10) { // 最多使用最近10条消息作为上下文
            if agent == .see, message.isFromUser, let imgData = image ?? message.imageData {
                // 为多模态模型准备图文混合内容
                let contentArray: [APIRequest.Message.Content] = [
                    .text(message.content),
                    .imageUrl(APIRequest.Message.ImageURL(url: "data:image/jpeg;base64,\(imgData.base64EncodedString())"))
                ]
                apiMessages.append(APIRequest.Message(role: "user", content: .array(contentArray)))
            } else {
                apiMessages.append(APIRequest.Message(role: message.isFromUser ? "user" : "assistant", content: .string(message.content)))
            }
        }
        
        return apiMessages
    }

    /// 执行对火山方舟API的调用。
    private func callAPI(with agent: AgentIdentifier, messages: [APIRequest.Message]) async -> String {
        guard let url = URL(string: baseURL) else {
            return "URL错误"
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        let requestBody = APIRequest(model: agent.rawValue, messages: messages)
        
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            request.httpBody = try encoder.encode(requestBody)
            
            if let jsonString = String(data: request.httpBody!, encoding: .utf8) {
                 print("Request Body for \(agent.endpointName):\n\(jsonString)")
            }

            let (data, response) = try await urlSession.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
                let errorBody = String(data: data, encoding: .utf8) ?? "无法解析错误信息"
                print("API请求失败: \(response) \nBody: \(errorBody)")
                return "请求失败: \( (response as? HTTPURLResponse)?.statusCode ?? -1)"
            }

            let apiResponse = try JSONDecoder().decode(MultiAgentAPIResponse.self, from: data)
            return apiResponse.choices.first?.message.content ?? "没有收到有效回复"
        } catch {
            print("API调用错误: \(error)")
            return "请求错误: \(error.localizedDescription)"
        }
    }
}

// MARK: - Helper Structures

extension MultiAgentService {
    
    enum UserIntent {
        case simpleChat
        case deepChat
        case imageQuery
        case unknown
    }

    // API请求体模型
    struct APIRequest: Codable {
        let model: String
        let messages: [Message]
        let stream: Bool?

        init(model: String, messages: [Message], stream: Bool? = nil) {
            self.model = model
            self.messages = messages
            self.stream = stream
        }
        
        struct Message: Codable {
            let role: String
            var content: ContentType

            enum ContentType: Codable {
                case string(String)
                case array([Content])

                func encode(to encoder: Encoder) throws {
                    var container = encoder.singleValueContainer()
                    switch self {
                    case .string(let value):
                        try container.encode(value)
                    case .array(let value):
                        try container.encode(value)
                    }
                }
                
                init(from decoder: Decoder) throws {
                    if let string = try? decoder.singleValueContainer().decode(String.self) {
                        self = .string(string)
                        return
                    }
                    if let array = try? decoder.singleValueContainer().decode([Content].self) {
                        self = .array(array)
                        return
                    }
                    throw DecodingError.typeMismatch(ContentType.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Unsupported content type"))
                }
            }
            
            enum Content: Codable {
                case text(String)
                case imageUrl(ImageURL)

                enum CodingKeys: String, CodingKey {
                    case type, text, image_url
                }

                func encode(to encoder: Encoder) throws {
                    var container = encoder.container(keyedBy: CodingKeys.self)
                    switch self {
                    case .text(let text):
                        try container.encode("text", forKey: .type)
                        try container.encode(text, forKey: .text)
                    case .imageUrl(let imageUrl):
                        try container.encode("image_url", forKey: .type)
                        try container.encode(imageUrl, forKey: .image_url)
                    }
                }
                
                init(from decoder: Decoder) throws {
                    let container = try decoder.container(keyedBy: CodingKeys.self)
                    let type = try container.decode(String.self, forKey: .type)
                    switch type {
                    case "text":
                        let text = try container.decode(String.self, forKey: .text)
                        self = .text(text)
                    case "image_url":
                        let imageUrl = try container.decode(ImageURL.self, forKey: .image_url)
                        self = .imageUrl(imageUrl)
                    default:
                        throw DecodingError.dataCorruptedError(forKey: .type, in: container, debugDescription: "Invalid content type")
                    }
                }
            }

            struct ImageURL: Codable {
                let url: String
            }
        }
    }
}

// MARK: - API响应模型（本地定义，避免与AgentScheduler冲突）

/// 多智能体服务专用的API响应模型
private struct MultiAgentAPIResponse: Codable {
    let choices: [Choice]

    struct Choice: Codable {
        let message: Message

        struct Message: Codable {
            let content: String
        }
    }
}
