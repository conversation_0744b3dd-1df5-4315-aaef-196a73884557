//
//  JSONTaskScheduler.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

/// JSON格式的任务调度器
/// 根据TaskAssignmentAgent返回的JSON决策，并行调用相应的智能体
@MainActor
class JSONTaskScheduler: ObservableObject {
    
    // MARK: - 依赖注入
    private let sharedState: SharedStateHub
    private let agentScheduler: AgentScheduler
    
    init(sharedState: SharedStateHub, agentScheduler: AgentScheduler) {
        self.sharedState = sharedState
        self.agentScheduler = agentScheduler
    }
    
    // MARK: - 主要处理方法
    
    /// 根据JSON任务决策并行执行智能体
    func executeTasksFromJSON(_ taskDecision: TaskDecision, userInput: UserInput) async -> AIResponse {
        print("🚀 开始并行执行任务...")
        
        let startTime = Date()
        var tasks: [Task<AgentResult, Never>] = []
        
        // 1. 创建聊天智能体任务（必须）
        let chatTask = createChatAgentTask(taskDecision, userInput)
        tasks.append(chatTask)
        
        // 2. 创建情感智能体任务（必须）
        let emotionTask = createEmotionAgentTask(taskDecision, userInput)
        tasks.append(emotionTask)
        
        // 3. 创建行动规划智能体任务（可选）
        if taskDecision.actionPlanningAgent {
            let actionTask = createActionPlanningTask(taskDecision, userInput)
            tasks.append(actionTask)
        }
        
        // 4. 等待所有任务完成
        var results: [AgentResult] = []
        for task in tasks {
            let result = await task.value
            results.append(result)
            print("✅ \(result.agentType) 执行完成，耗时: \(String(format: "%.2f", result.processingTime))秒")
        }
        
        // 5. 整合结果
        let totalTime = Date().timeIntervalSince(startTime)
        let response = integrateResults(results, taskDecision: taskDecision, totalTime: totalTime, userMessage: userInput.message)
        
        print("🎯 所有任务执行完成，总耗时: \(String(format: "%.2f", totalTime))秒")
        
        return response
    }
    
    // MARK: - 任务创建方法
    
    /// 创建聊天智能体任务
    private func createChatAgentTask(_ taskDecision: TaskDecision, _ userInput: UserInput) -> Task<AgentResult, Never> {
        return Task {
            let startTime = Date()
            
            // 准备聊天文本（包含上下文信息）
            let chatText = await prepareChatText(taskDecision, userInput)
            
            // 选择智能体
            let agentId: AgentIdentifier = taskDecision.chatAgent == "deep" ? .deepThinking : .easyCommunication
            
            // 创建输入
            let agentInput = AgentInput(
                userMessage: chatText,
                messageType: userInput.messageType,
                imageData: userInput.imageData,
                context: sharedState.currentContext
            )
            
            // 执行智能体
            if let agent = agentScheduler.agents[agentId] {
                let output = await agent.process(agentInput)
                let processingTime = Date().timeIntervalSince(startTime)
                
                return AgentResult(
                    agentType: taskDecision.chatAgent == "deep" ? "深度思考智能体" : "简单沟通智能体",
                    content: output.content,
                    confidence: output.confidence,
                    ttsEmotion: nil,
                    processingTime: processingTime,
                    metadata: output.metadata
                )
            } else {
                return AgentResult(
                    agentType: "聊天智能体",
                    content: "抱歉，处理出现问题",
                    confidence: 0.1,
                    ttsEmotion: nil,
                    processingTime: Date().timeIntervalSince(startTime),
                    metadata: [:]
                )
            }
        }
    }
    
    /// 创建情感智能体任务
    private func createEmotionAgentTask(_ taskDecision: TaskDecision, _ userInput: UserInput) -> Task<AgentResult, Never> {
        return Task {
            let startTime = Date()
            
            // 创建输入
            let agentInput = AgentInput(
                userMessage: taskDecision.emotionText,
                messageType: userInput.messageType,
                imageData: userInput.imageData,
                context: sharedState.currentContext
            )
            
            // 执行情感智能体
            if let agent = agentScheduler.agents[.emotionPerception] {
                let output = await agent.process(agentInput)
                let processingTime = Date().timeIntervalSince(startTime)
                
                // 从输出中提取TTS情感
                let ttsEmotion = TTSEmotionConfig.getMatchingEmotion(for: taskDecision.ttsEmotion)
                
                return AgentResult(
                    agentType: "情感感知智能体",
                    content: output.content,
                    confidence: output.confidence,
                    ttsEmotion: ttsEmotion,
                    processingTime: processingTime,
                    metadata: output.metadata
                )
            } else {
                return AgentResult(
                    agentType: "情感感知智能体",
                    content: "情感分析完成",
                    confidence: 0.8,
                    ttsEmotion: TTSEmotionConfig.getMatchingEmotion(for: taskDecision.ttsEmotion),
                    processingTime: Date().timeIntervalSince(startTime),
                    metadata: [:]
                )
            }
        }
    }
    
    /// 创建行动规划任务
    private func createActionPlanningTask(_ taskDecision: TaskDecision, _ userInput: UserInput) -> Task<AgentResult, Never> {
        return Task {
            let startTime = Date()
            
            guard let actionText = taskDecision.actionPlanningText else {
                return AgentResult(
                    agentType: "AI行动规划智能体",
                    content: "",
                    confidence: 0.0,
                    ttsEmotion: nil,
                    processingTime: 0.0,
                    metadata: [:]
                )
            }
            
            // 创建输入
            let agentInput = AgentInput(
                userMessage: actionText,
                messageType: userInput.messageType,
                imageData: userInput.imageData,
                context: sharedState.currentContext
            )
            
            // 执行行动规划智能体
            if let agent = agentScheduler.agents[.aiActionPlanning] {
                let output = await agent.process(agentInput)
                let processingTime = Date().timeIntervalSince(startTime)
                
                return AgentResult(
                    agentType: "AI行动规划智能体",
                    content: output.content,
                    confidence: output.confidence,
                    ttsEmotion: nil,
                    processingTime: processingTime,
                    metadata: output.metadata
                )
            } else {
                return AgentResult(
                    agentType: "AI行动规划智能体",
                    content: "行动规划更新完成",
                    confidence: 0.8,
                    ttsEmotion: nil,
                    processingTime: Date().timeIntervalSince(startTime),
                    metadata: [:]
                )
            }
        }
    }
    
    // MARK: - 辅助方法
    
    /// 准备聊天文本（包含必要的上下文）
    private func prepareChatText(_ taskDecision: TaskDecision, _ userInput: UserInput) async -> String {
        var chatText = taskDecision.chatText

        // 添加系统时间信息
        let currentTime = getCurrentSystemTime()
        chatText += "\n\n当前系统时间：\(currentTime)"

        // 根据决策添加上下文信息
        if taskDecision.includeSchedule {
            let schedule = sharedState.aiLifeSchedule.getCurrentLifeStatus()
            chatText += "\n\n当前AI时间安排：\(schedule)"
        }

        if taskDecision.includeAIPrompt {
            let aiPrompt = sharedState.aiPersonality.generateCurrentPersonality()
            chatText += "\n\n当前AI人格状态：\(aiPrompt)"
        }

        if taskDecision.includeAIMood {
            let mood = sharedState.aiPersonality.currentEmotion
            chatText += "\n\n当前AI心情：主要情感=\(mood.primary), 强度=\(mood.intensity)"
        }

        // 添加长期记忆检索结果
        chatText += await addLongTermMemoryContext(userInput.message)

        return chatText
    }

    /// 获取当前系统时间
    private func getCurrentSystemTime() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日 HH:mm:ss EEEE"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: Date())
    }

    /// 添加长期记忆上下文
    private func addLongTermMemoryContext(_ userMessage: String) async -> String {
        print("🧠 检索长期记忆相关内容...")

        // 检索相关记忆
        let relevantMemories = await sharedState.longTermMemory.retrieveRelevantMemories(userMessage, limit: 3)

        if !relevantMemories.isEmpty {
            var memoryContext = "\n\n相关的长期记忆："
            for memory in relevantMemories {
                memoryContext += "\n- \(memory.content)"
            }
            print("✅ 检索到 \(relevantMemories.count) 条相关长期记忆")
            return memoryContext
        } else {
            print("ℹ️ 未找到相关的长期记忆")
            return ""
        }
    }
    
    /// 整合所有智能体的结果
    private func integrateResults(_ results: [AgentResult], taskDecision: TaskDecision, totalTime: Double, userMessage: String) -> AIResponse {
        // 找到聊天智能体的结果作为主要内容
        let chatResult = results.first { $0.agentType.contains("智能体") && !$0.agentType.contains("情感") && !$0.agentType.contains("规划") }

        // 找到情感智能体的结果获取TTS情感
        let emotionResult = results.first { $0.agentType.contains("情感") }

        let content = chatResult?.content ?? "抱歉，处理过程中出现了问题"
        let confidence = chatResult?.confidence ?? 0.5
        let ttsEmotion = emotionResult?.ttsEmotion

        // 更新AI状态
        if let emotionResult = emotionResult {
            // 这里可以根据情感分析结果更新AI状态
            print("💭 情感分析结果已处理")
        }

        // 更新对话上下文
        sharedState.updateConversationContext(userMessage: userMessage, aiResponse: content)

        // 将agentType字符串转换为AgentIdentifier
        let executedAgentIds: [AgentIdentifier] = results.compactMap { result in
            if result.agentType.contains("简单沟通") {
                return .easyCommunication
            } else if result.agentType.contains("深度思考") {
                return .deepThinking
            } else if result.agentType.contains("情感感知") {
                return .emotionPerception
            } else if result.agentType.contains("行动规划") {
                return .aiActionPlanning
            } else {
                return .easyCommunication // 默认值
            }
        }

        return AIResponse(
            content: content,
            processingTime: totalTime,
            confidence: confidence,
            ttsEmotion: ttsEmotion,
            executedAgents: executedAgentIds,
            metadata: [
                "task_decision": taskDecision.reasoning,
                "parallel_execution": true,
                "agents_count": results.count
            ]
        )
    }
}

// MARK: - 智能体执行结果

/// 智能体执行结果
struct AgentResult {
    let agentType: String
    let content: String
    let confidence: Double
    let ttsEmotion: CanCanEmotion?
    let processingTime: Double
    let metadata: [String: Any]
}
