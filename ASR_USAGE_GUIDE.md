# ASR语音识别功能使用指南

## 功能概述

Travel With应用现已集成强大的语音识别功能，支持将您的语音实时转换为文字，并自动发送给AI朋友进行对话。

## 主要特性

### 🎤 双重识别引擎
- **iOS原生语音识别**：默认使用，稳定可靠，支持离线识别
- **火山引擎ASR**：备选方案，支持更高精度的在线识别

### 🔄 智能切换机制
- 默认优先使用iOS原生Speech框架
- 可根据需要切换到火山引擎ASR服务
- 自动错误处理和回退机制

### 🎯 实时反馈
- 录音时显示动态识别状态
- 实时显示部分识别结果
- 识别完成后自动发送给AI

## 使用方法

### 1. 开始语音输入
1. 在聊天界面点击左侧的麦克风图标切换到语音模式
2. 长按"按住说话"按钮开始录音
3. 对着麦克风清晰地说话

### 2. 录音过程
- 录音时按钮会变红并显示"正在识别..."
- 可以看到实时的识别状态动画
- 松开按钮即停止录音

### 3. 识别结果
- 识别完成后，文字会自动发送给AI朋友
- AI会像平常一样回复您的消息
- 支持中文语音识别

## 权限要求

### 必需权限
- **麦克风权限**：用于录制语音
- **语音识别权限**：用于将语音转换为文字

### 权限申请
应用会在首次使用语音功能时自动申请相关权限，请允许以获得最佳体验。

## 技术配置

### 火山引擎ASR配置
```
APP ID: 1771796339
Access Token: MduxvCjM28XnWnXS_a2_NAedHCJ9649D
Secret Key: fV6rw3JgtauLQ9mOy_p6u2rX3PlmerAt
WebSocket URL: wss://openspeech.bytedance.com/api/v2/asr
```

### 音频参数
- **采样率**：16000 Hz
- **位深度**：16 bit
- **声道数**：单声道
- **格式**：PCM Raw

## 故障排除

### 常见问题

#### 1. 无法开始录音
- **检查麦克风权限**：设置 > 隐私与安全性 > 麦克风
- **检查语音识别权限**：设置 > 隐私与安全性 > 语音识别

#### 2. 识别不准确
- **说话清晰**：尽量清晰地发音
- **环境安静**：在安静的环境中使用
- **距离适中**：保持适当的麦克风距离

#### 3. 识别失败
- **网络连接**：确保网络连接正常（火山引擎ASR需要网络）
- **重试操作**：可以重新录音尝试
- **切换模式**：iOS原生识别支持离线使用

### 错误代码说明
- **权限错误**：需要授权麦克风和语音识别权限
- **网络错误**：火山引擎ASR连接失败，会自动回退到iOS原生识别
- **音频错误**：音频设备初始化失败，请检查设备状态

## 开发者信息

### 文件结构
```
travel with/
├── ASRService.swift          # ASR服务管理器
├── AIService.swift           # AI服务（集成ASR）
├── AIChatView.swift          # 聊天界面（语音UI）
├── Info.plist               # 权限配置
└── ASRServiceTests.swift     # 单元测试
```

### 核心类说明

#### ASRService
- 管理语音识别的核心服务
- 支持iOS原生和火山引擎双重识别
- 提供完整的回调机制

#### AIService集成
- 集成ASR服务到AI对话流程
- 自动处理识别结果并发送给AI
- 统一的错误处理和状态管理

## 更新日志

### v1.0.0 (2025/7/31)
- ✅ 集成火山引擎ASR服务
- ✅ 添加iOS原生语音识别备选
- ✅ 实现实时语音转文字功能
- ✅ 优化录音界面和用户体验
- ✅ 完善权限管理和错误处理
- ✅ 添加完整的单元测试覆盖

## 技术支持

如果您在使用过程中遇到问题，请检查：
1. 设备权限设置
2. 网络连接状态
3. 应用版本是否最新
4. 设备兼容性（iOS 15.0+）

---

**注意**：语音识别功能需要iOS 15.0或更高版本，建议在安静环境中使用以获得最佳识别效果。
