//
//  ParallelTTSTests.swift
//  travel withTests
//
//  Created by Multi-Agent System on 2025/8/3.
//

import XCTest
@testable import travel_with

/// 并行TTS系统测试
final class ParallelTTSTests: XCTestCase {
    
    var streamingTTSManager: StreamingTTSManager!
    var mockTTSService: MockTTSService!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        mockTTSService = MockTTSService()
        streamingTTSManager = StreamingTTSManager(ttsService: mockTTSService)
    }
    
    override func tearDownWithError() throws {
        streamingTTSManager = nil
        mockTTSService = nil
        try super.tearDownWithError()
    }
    
    // MARK: - 文本分割测试

    func testSmallTextNoSplit() throws {
        let text = "这是一个短文本。"

        let expectation = XCTestExpectation(description: "小文本处理完成")

        streamingTTSManager.onPlaybackCompleted = {
            expectation.fulfill()
        }

        Task {
            await streamingTTSManager.startStreamingPlayback(text, emotion: .pleased)
        }

        wait(for: [expectation], timeout: 5.0)

        // 验证只有一个请求被处理（小文本不需要分割）
        XCTAssertEqual(mockTTSService.processedRequests.count, 1)
        XCTAssertEqual(mockTTSService.processedRequests.first?.text, text)
    }
    
    func testLargeTextSplit() throws {
        // 创建一个超过1024字节的文本
        let longText = String(repeating: "这是一个很长的句子，用来测试文本分割功能。", count: 30) + "这是最后一句。"
        
        let expectation = XCTestExpectation(description: "长文本处理完成")
        
        streamingTTSManager.onPlaybackCompleted = {
            expectation.fulfill()
        }
        
        Task {
            await streamingTTSManager.startStreamingPlayback(longText, emotion: .pleased)
        }
        
        wait(for: [expectation], timeout: 10.0)
        
        // 验证文本被分割成多个片段
        XCTAssertGreaterThan(mockTTSService.processedRequests.count, 1)
        
        // 验证每个片段都不超过1024字节
        for request in mockTTSService.processedRequests {
            let segmentData = request.text.data(using: .utf8) ?? Data()
            XCTAssertLessThanOrEqual(segmentData.count, 1024, "片段超过1024字节限制")
        }
    }
    
    // MARK: - 并行处理测试
    
    func testParallelProcessing() throws {
        let text = "第一句话。第二句话。第三句话。"
        let expectation = XCTestExpectation(description: "并行TTS处理完成")
        
        streamingTTSManager.onPlaybackCompleted = {
            expectation.fulfill()
        }
        
        Task {
            await streamingTTSManager.startStreamingPlayback(text, emotion: .pleased)
        }
        
        wait(for: [expectation], timeout: 10.0)
        
        // 验证所有片段都被处理
        XCTAssertGreaterThan(mockTTSService.processedRequests.count, 0)
        
        // 验证每个请求都有唯一的reqid
        let reqIds = mockTTSService.processedRequests.map { $0.reqId }
        let uniqueReqIds = Set(reqIds)
        XCTAssertEqual(reqIds.count, uniqueReqIds.count, "reqid应该是唯一的")
    }
    
    // MARK: - 顺序播放测试
    
    func testOrderedPlayback() throws {
        let text = "第一句。第二句。第三句。"
        var playbackOrder: [Int] = []
        
        streamingTTSManager.onPlaybackProgress = { current, total in
            playbackOrder.append(current)
        }
        
        let expectation = XCTestExpectation(description: "顺序播放完成")
        streamingTTSManager.onPlaybackCompleted = {
            expectation.fulfill()
        }
        
        Task {
            await streamingTTSManager.startStreamingPlayback(text, emotion: .pleased)
        }
        
        wait(for: [expectation], timeout: 10.0)
        
        // 验证播放顺序是正确的（应该是连续递增的）
        XCTAssertFalse(playbackOrder.isEmpty, "应该有播放进度记录")
        
        // 验证顺序是递增的
        for i in 1..<playbackOrder.count {
            XCTAssertGreaterThan(playbackOrder[i], playbackOrder[i-1], "播放顺序应该是递增的")
        }
    }
    
    // MARK: - 错误处理测试
    
    func testErrorHandling() throws {
        mockTTSService.shouldFail = true
        
        let text = "测试错误处理。"
        let expectation = XCTestExpectation(description: "错误处理完成")
        
        streamingTTSManager.onPlaybackCompleted = {
            expectation.fulfill()
        }
        
        Task {
            await streamingTTSManager.startStreamingPlayback(text, emotion: .pleased)
        }
        
        wait(for: [expectation], timeout: 10.0)
        
        // 验证即使出错也能完成处理
        XCTAssertGreaterThan(mockTTSService.processedRequests.count, 0)
    }
}

// MARK: - Mock TTS Service

class MockTTSService: TTSService {
    
    struct ProcessedRequest {
        let text: String
        let emotion: CanCanEmotion
        let reqId: String
    }
    
    var processedRequests: [ProcessedRequest] = []
    var shouldFail: Bool = false
    
    override func synthesizeTextToDataWithReqId(text: String, emotion: CanCanEmotion, reqId: String) async -> Data? {
        // 记录处理的请求
        let request = ProcessedRequest(text: text, emotion: emotion, reqId: reqId)
        processedRequests.append(request)
        
        // 模拟异步处理延迟
        try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
        
        // 模拟错误情况
        if shouldFail {
            return nil
        }
        
        // 返回模拟的音频数据
        return "mock_audio_data_\(reqId)".data(using: .utf8)
    }
}
