//
//  MultiAgentSystemTests.swift
//  travel withTests
//
//  Created by Multi-Agent System on 2025/8/2.
//

import XCTest
@testable import travel_with

/// 多智能体系统的基础测试
@MainActor
final class MultiAgentSystemTests: XCTestCase {
    
    var sharedStateHub: SharedStateHub!
    var agentScheduler: AgentScheduler!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // 初始化测试环境
        sharedStateHub = SharedStateHub()
        agentScheduler = AgentScheduler(sharedState: sharedStateHub)
        
        // 等待系统初始化完成
        var attempts = 0
        while !sharedStateHub.isSystemReady() && attempts < 50 {
            try await Task.sleep(nanoseconds: 100_000_000) // 等待100ms
            attempts += 1
        }
        
        XCTAssertTrue(sharedStateHub.isSystemReady(), "共享状态中心应该在合理时间内初始化完成")
    }
    
    override func tearDown() async throws {
        sharedStateHub = nil
        agentScheduler = nil
        try await super.tearDown()
    }
    
    // MARK: - 共享状态中心测试
    
    func testSharedStateHubInitialization() async throws {
        // 测试共享状态中心是否正确初始化
        XCTAssertTrue(sharedStateHub.isSystemReady(), "共享状态中心应该已就绪")
        XCTAssertNotNil(sharedStateHub.longTermMemory, "长期记忆管理器应该已初始化")
        XCTAssertNotNil(sharedStateHub.aiPersonality, "AI人格管理器应该已初始化")
        XCTAssertNotNil(sharedStateHub.aiLifeSchedule, "AI生活日程管理器应该已初始化")
    }
    
    func testConversationContextUpdate() async throws {
        // 测试对话上下文更新
        let testMessage = "你好，今天天气怎么样？"
        let testResponse = "今天天气很好！"
        
        sharedStateHub.updateConversationContext(userMessage: testMessage, aiResponse: testResponse)
        
        let context = sharedStateHub.currentContext
        XCTAssertFalse(context.conversationHistory.isEmpty, "对话历史应该包含消息")
        
        let lastMessage = context.conversationHistory.last
        XCTAssertEqual(lastMessage?.userMessage, testMessage, "用户消息应该正确保存")
        XCTAssertEqual(lastMessage?.aiResponse, testResponse, "AI回复应该正确保存")
    }
    
    // MARK: - AI人格管理器测试
    
    func testAIPersonalityManager() async throws {
        let personalityManager = sharedStateHub.aiPersonality
        
        // 测试人格生成
        let personality = personalityManager.generateCurrentPersonality()
        XCTAssertFalse(personality.isEmpty, "生成的人格描述不应为空")
        XCTAssertTrue(personality.contains("小旅"), "人格描述应该包含AI名称")
        
        // 测试情感更新
        let newEmotion = EmotionState(primary: .happy, intensity: 0.8, secondary: [.excited])
        personalityManager.updateEmotion(newEmotion)
        
        XCTAssertEqual(personalityManager.currentEmotion.primary, .happy, "情感状态应该正确更新")
        XCTAssertEqual(personalityManager.currentEmotion.intensity, 0.8, accuracy: 0.01, "情感强度应该正确设置")
    }
    
    // MARK: - AI生活日程管理器测试
    
    func testAILifeScheduleManager() async throws {
        let scheduleManager = sharedStateHub.aiLifeSchedule
        
        // 测试生活状态描述生成
        let lifeStatus = scheduleManager.getCurrentLifeStatus()
        XCTAssertFalse(lifeStatus.isEmpty, "生活状态描述不应为空")
        
        // 测试活动更新
        let testActivity = LifeActivity(
            id: UUID(),
            name: "测试活动",
            description: "这是一个测试活动",
            startTime: Date(),
            endTime: Date().addingTimeInterval(1800),
            category: .leisure,
            status: .inProgress
        )
        
        scheduleManager.updateCurrentActivity(testActivity)
        XCTAssertEqual(scheduleManager.currentActivity?.name, "测试活动", "当前活动应该正确更新")
    }
    
    // MARK: - 智能体调度器测试
    
    func testAgentSchedulerInitialization() async throws {
        // 等待调度器初始化完成
        var attempts = 0
        while !agentScheduler.isReady && attempts < 50 {
            try await Task.sleep(nanoseconds: 100_000_000)
            attempts += 1
        }
        
        XCTAssertTrue(agentScheduler.isReady, "智能体调度器应该在合理时间内初始化完成")
    }
    
    func testUserInputProcessing() async throws {
        // 等待调度器就绪
        var attempts = 0
        while !agentScheduler.isReady && attempts < 50 {
            try await Task.sleep(nanoseconds: 100_000_000)
            attempts += 1
        }
        
        guard agentScheduler.isReady else {
            XCTFail("调度器未能在合理时间内初始化完成")
            return
        }
        
        // 测试简单文本输入处理
        let userInput = UserInput(message: "你好", messageType: .text)
        let response = await agentScheduler.processUserInput(userInput)
        
        XCTAssertFalse(response.content.isEmpty, "AI回复不应为空")
        XCTAssertGreaterThan(response.processingTime, 0, "处理时间应该大于0")
        XCTAssertFalse(response.executedAgents.isEmpty, "应该有智能体参与处理")
    }
    
    // MARK: - 长期记忆管理器测试
    
    func testLongTermMemoryManager() async throws {
        let memoryManager = sharedStateHub.longTermMemory
        
        // 等待记忆管理器就绪
        var attempts = 0
        while !memoryManager.isReady && attempts < 30 {
            try await Task.sleep(nanoseconds: 100_000_000)
            attempts += 1
        }
        
        // 测试记忆检索
        let testQuery = "旅行"
        let memories = await memoryManager.retrieveRelevantMemories(testQuery, limit: 5)
        
        // 由于是新系统，可能没有历史记忆，所以只测试方法是否正常执行
        XCTAssertNotNil(memories, "记忆检索应该返回结果（即使为空）")
        XCTAssertLessThanOrEqual(memories.count, 5, "返回的记忆数量不应超过限制")
    }
    
    // MARK: - 性能测试
    
    func testSystemPerformance() async throws {
        // 等待系统完全就绪
        var attempts = 0
        while (!sharedStateHub.isSystemReady() || !agentScheduler.isReady) && attempts < 50 {
            try await Task.sleep(nanoseconds: 100_000_000)
            attempts += 1
        }
        
        guard sharedStateHub.isSystemReady() && agentScheduler.isReady else {
            XCTFail("系统未能在合理时间内初始化完成")
            return
        }
        
        // 测试多个连续请求的处理性能
        let startTime = Date()
        let testMessages = ["你好", "今天天气怎么样？", "推荐一个旅行地点"]
        
        for message in testMessages {
            let userInput = UserInput(message: message, messageType: .text)
            let response = await agentScheduler.processUserInput(userInput)
            XCTAssertFalse(response.content.isEmpty, "每个回复都不应为空")
        }
        
        let totalTime = Date().timeIntervalSince(startTime)
        XCTAssertLessThan(totalTime, 30.0, "处理3个消息的总时间应该在30秒内")
    }
    
    // MARK: - 错误处理测试
    
    func testErrorHandling() async throws {
        // 测试空消息处理
        let emptyInput = UserInput(message: "", messageType: .text)
        let response = await agentScheduler.processUserInput(emptyInput)
        
        // 系统应该能处理空消息而不崩溃
        XCTAssertNotNil(response, "即使是空消息也应该有响应")
    }
    
    // MARK: - 状态查询测试
    
    func testStatusQueries() async throws {
        // 测试系统状态查询
        let hubStatus = sharedStateHub.getSystemStatusSummary()
        XCTAssertFalse(hubStatus.isEmpty, "系统状态摘要不应为空")
        
        let schedulerStatus = agentScheduler.getStatusSummary()
        XCTAssertFalse(schedulerStatus.isEmpty, "调度器状态摘要不应为空")
        
        let personalityStatus = sharedStateHub.aiPersonality.getStatusSummary()
        XCTAssertFalse(personalityStatus.isEmpty, "人格管理器状态摘要不应为空")
        
        let scheduleStatus = sharedStateHub.aiLifeSchedule.getStatusSummary()
        XCTAssertFalse(scheduleStatus.isEmpty, "生活日程管理器状态摘要不应为空")
    }
}
