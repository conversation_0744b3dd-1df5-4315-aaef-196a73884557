//
//  MapNavigationTests.swift
//  travel withTests
//
//  Created by AI Assistant on 2025/7/30.
//

import XCTest
import MapKit
import CoreLocation
@testable import travel_with

final class MapNavigationTests: XCTestCase {
    
    var locationManager: LocationManager!
    
    override func setUpWithError() throws {
        locationManager = LocationManager()
    }
    
    override func tearDownWithError() throws {
        locationManager = nil
    }
    
    // MARK: - 交通方式测试
    func testTransportTypeProperties() throws {
        // 测试所有交通方式的属性
        let transportTypes = MapNavigationView.TransportType.allCases
        
        XCTAssertEqual(transportTypes.count, 5, "应该有5种交通方式")
        
        // 测试每种交通方式的图标和MK类型
        for transportType in transportTypes {
            XCTAssertFalse(transportType.icon.isEmpty, "\(transportType.rawValue) 应该有图标")
            XCTAssertNotNil(transportType.mkTransportType, "\(transportType.rawValue) 应该有对应的MK类型")
        }
    }
    
    func testTransportTypeIcons() throws {
        // 测试交通方式图标
        XCTAssertEqual(MapNavigationView.TransportType.automobile.icon, "car.fill")
        XCTAssertEqual(MapNavigationView.TransportType.walking.icon, "figure.walk")
        XCTAssertEqual(MapNavigationView.TransportType.transit.icon, "bus.fill")
        XCTAssertEqual(MapNavigationView.TransportType.cycling.icon, "bicycle")
        XCTAssertEqual(MapNavigationView.TransportType.rideshare.icon, "car.2.fill")
    }
    
    func testTransportTypeMKTypes() throws {
        // 测试MKDirectionsTransportType映射
        XCTAssertEqual(MapNavigationView.TransportType.automobile.mkTransportType, .automobile)
        XCTAssertEqual(MapNavigationView.TransportType.walking.mkTransportType, .walking)
        XCTAssertEqual(MapNavigationView.TransportType.transit.mkTransportType, .transit)
        XCTAssertEqual(MapNavigationView.TransportType.cycling.mkTransportType, .automobile)
        XCTAssertEqual(MapNavigationView.TransportType.rideshare.mkTransportType, .automobile)
    }
    
    func testTransportTypeRawValues() throws {
        // 测试交通方式的中文名称
        XCTAssertEqual(MapNavigationView.TransportType.automobile.rawValue, "开车")
        XCTAssertEqual(MapNavigationView.TransportType.walking.rawValue, "步行")
        XCTAssertEqual(MapNavigationView.TransportType.transit.rawValue, "公交")
        XCTAssertEqual(MapNavigationView.TransportType.cycling.rawValue, "骑车")
        XCTAssertEqual(MapNavigationView.TransportType.rideshare.rawValue, "打车")
    }
    
    // MARK: - 路线选项测试
    func testRouteOptionCreation() throws {
        // 创建模拟路线选项
        let mockRoute = MKRoute()
        let transportType = MapNavigationView.TransportType.automobile
        
        let routeOption = MapNavigationView.RouteOption(
            route: mockRoute,
            transportType: transportType,
            duration: 1800, // 30分钟
            distance: 15000, // 15公里
            description: "15.0 km · 30 分钟"
        )
        
        XCTAssertEqual(routeOption.transportType, transportType)
        XCTAssertEqual(routeOption.duration, 1800)
        XCTAssertEqual(routeOption.distance, 15000)
        XCTAssertEqual(routeOption.description, "15.0 km · 30 分钟")
    }
    
    // MARK: - 打车选项测试
    func testRideOptions() throws {
        let rideOptions = [
            RideOption(name: "滴滴出行", icon: "car.fill", color: .orange, urlScheme: "diditaxi://"),
            RideOption(name: "高德地图", icon: "map.fill", color: .blue, urlScheme: "iosamap://"),
            RideOption(name: "百度地图", icon: "location.fill", color: .red, urlScheme: "baidumap://"),
            RideOption(name: "腾讯地图", icon: "mappin.circle.fill", color: .green, urlScheme: "qqmap://"),
            RideOption(name: "Apple地图", icon: "map", color: .gray, urlScheme: "maps://")
        ]
        
        XCTAssertEqual(rideOptions.count, 5, "应该有5个打车选项")
        
        // 测试每个选项的属性
        for option in rideOptions {
            XCTAssertFalse(option.name.isEmpty, "打车选项名称不应为空")
            XCTAssertFalse(option.icon.isEmpty, "打车选项图标不应为空")
            XCTAssertFalse(option.urlScheme.isEmpty, "打车选项URL scheme不应为空")
        }
    }
    
    func testRideOptionURLSchemes() throws {
        // 测试URL Scheme格式
        let schemes = ["diditaxi://", "iosamap://", "baidumap://", "qqmap://", "maps://"]
        
        for scheme in schemes {
            XCTAssertTrue(scheme.hasSuffix("://"), "URL Scheme应该以://结尾")
            XCTAssertFalse(scheme.isEmpty, "URL Scheme不应为空")
        }
    }
    
    // MARK: - 位置管理器测试
    func testLocationManagerInitialization() throws {
        XCTAssertNotNil(locationManager, "位置管理器应该能够初始化")
        XCTAssertEqual(locationManager.authorizationStatus, .notDetermined, "初始授权状态应该是未确定")
        XCTAssertNil(locationManager.userLocation, "初始用户位置应该为空")
    }
    
    // MARK: - 坐标验证测试
    func testCoordinateValidation() throws {
        // 测试坐标验证
        let validCoordinate = CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074)
        let invalidCoordinate = CLLocationCoordinate2D(latitude: 200, longitude: 200)
        
        XCTAssertTrue(CLLocationCoordinate2DIsValid(validCoordinate), "有效坐标应该通过验证")
        XCTAssertFalse(CLLocationCoordinate2DIsValid(invalidCoordinate), "无效坐标应该不通过验证")
    }
    
    func testDistanceCalculation() throws {
        // 测试距离计算
        let location1 = CLLocation(latitude: 39.9042, longitude: 116.4074) // 北京天安门
        let location2 = CLLocation(latitude: 39.9142, longitude: 116.4174) // 附近位置
        
        let distance = location1.distance(from: location2)
        XCTAssertGreaterThan(distance, 0, "距离应该大于0")
        XCTAssertLessThan(distance, 2000, "距离应该小于2公里")
    }
    
    // MARK: - 性能测试
    func testTransportTypePerformance() throws {
        // 测试交通方式枚举的性能
        measure {
            for _ in 0..<1000 {
                let _ = MapNavigationView.TransportType.allCases
            }
        }
    }
    
    func testRouteOptionPerformance() throws {
        // 测试路线选项创建的性能
        let mockRoute = MKRoute()
        
        measure {
            for i in 0..<100 {
                let _ = MapNavigationView.RouteOption(
                    route: mockRoute,
                    transportType: .automobile,
                    duration: TimeInterval(i * 60),
                    distance: CLLocationDistance(i * 1000),
                    description: "\(i) km · \(i) 分钟"
                )
            }
        }
    }
}
