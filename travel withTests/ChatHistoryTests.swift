//
//  ChatHistoryTests.swift
//  travel withTests
//
//  Created by AI Assistant on 2025/7/30.
//

import XCTest
import SwiftData
@testable import travel_with

@MainActor
final class ChatHistoryTests: XCTestCase {
    
    var historyManager: ChatHistoryManager!
    var testContainer: ModelContainer!
    
    override func setUpWithError() throws {
        // 创建内存中的测试容器
        let schema = Schema([ChatHistory.self])
        let config = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
        testContainer = try ModelContainer(for: schema, configurations: [config])
        
        historyManager = ChatHistoryManager()
        // 手动设置测试用的ModelContext
        let mirror = Mirror(reflecting: historyManager)
        if let modelContextProperty = mirror.children.first(where: { $0.label == "modelContext" }) {
            // 通过反射设置私有属性（仅用于测试）
            historyManager.setValue(ModelContext(testContainer), forKey: "modelContext")
        }
    }
    
    override func tearDownWithError() throws {
        historyManager = nil
        testContainer = nil
    }
    
    // MARK: - 基础功能测试
    
    func testSaveMessage() async throws {
        let message = ChatMessage(content: "测试消息", isFromUser: true, messageType: .text)
        
        historyManager.saveMessage(message)
        
        // 验证消息已保存
        await historyManager.loadRecentMessages()
        XCTAssertEqual(historyManager.recentMessages.count, 1)
        XCTAssertEqual(historyManager.recentMessages.first?.content, "测试消息")
        XCTAssertTrue(historyManager.recentMessages.first?.isFromUser ?? false)
    }
    
    func testSaveImageMessage() async throws {
        let message = ChatMessage(content: "", isFromUser: true, messageType: .image)
        let imageDescription = "[图像]:一张美丽的风景照"
        
        historyManager.saveMessage(message, imageDescription: imageDescription)
        
        await historyManager.loadRecentMessages()
        XCTAssertEqual(historyManager.recentMessages.count, 1)
        XCTAssertEqual(historyManager.recentMessages.first?.imageDescription, imageDescription)
        XCTAssertEqual(historyManager.recentMessages.first?.messageType, "image")
    }
    
    func testLoadRecentMessages() async throws {
        // 创建多条测试消息
        for i in 1...25 {
            let message = ChatMessage(content: "消息 \(i)", isFromUser: i % 2 == 0, messageType: .text)
            historyManager.saveMessage(message)
        }
        
        // 加载最近消息（应该只加载20条）
        await historyManager.loadRecentMessages()
        
        XCTAssertEqual(historyManager.recentMessages.count, 20)
        XCTAssertTrue(historyManager.hasMoreHistory)
        
        // 验证消息顺序（最新的在后面）
        XCTAssertEqual(historyManager.recentMessages.last?.content, "消息 25")
    }
    
    func testLoadMoreHistory() async throws {
        // 创建30条测试消息
        for i in 1...30 {
            let message = ChatMessage(content: "消息 \(i)", isFromUser: i % 2 == 0, messageType: .text)
            historyManager.saveMessage(message)
        }
        
        // 首次加载
        await historyManager.loadRecentMessages()
        XCTAssertEqual(historyManager.recentMessages.count, 20)
        
        // 加载更多
        await historyManager.loadMoreHistory()
        XCTAssertEqual(historyManager.recentMessages.count, 30)
        XCTAssertFalse(historyManager.hasMoreHistory)
        
        // 验证消息顺序
        XCTAssertEqual(historyManager.recentMessages.first?.content, "消息 1")
        XCTAssertEqual(historyManager.recentMessages.last?.content, "消息 30")
    }
    
    func testGetRAGContext() async throws {
        // 创建测试对话
        let messages = [
            ("用户: 你好", true),
            ("AI: 嗨！今天想去哪玩呀？", false),
            ("用户: 想去海边", true),
            ("AI: 哇海边！天气这么好去海边绝了", false)
        ]
        
        for (content, isFromUser) in messages {
            let message = ChatMessage(content: content, isFromUser: isFromUser, messageType: .text)
            historyManager.saveMessage(message)
        }
        
        // 获取RAG上下文
        let ragContext = await historyManager.getRAGContext(maxMessages: 4)
        
        XCTAssertFalse(ragContext.isEmpty)
        XCTAssertTrue(ragContext.contains("用户: 你好"))
        XCTAssertTrue(ragContext.contains("AI: 嗨！今天想去哪玩呀？"))
        XCTAssertTrue(ragContext.contains("用户: 想去海边"))
        XCTAssertTrue(ragContext.contains("AI: 哇海边！天气这么好去海边绝了"))
    }
    
    func testClearHistory() async throws {
        // 创建测试消息
        for i in 1...5 {
            let message = ChatMessage(content: "消息 \(i)", isFromUser: true, messageType: .text)
            historyManager.saveMessage(message)
        }
        
        await historyManager.loadRecentMessages()
        XCTAssertEqual(historyManager.recentMessages.count, 5)
        
        // 清空历史
        await historyManager.clearHistory()
        
        XCTAssertEqual(historyManager.recentMessages.count, 0)
        XCTAssertTrue(historyManager.hasMoreHistory)
    }
    
    func testSessionStats() async throws {
        // 创建测试消息
        for i in 1...3 {
            let message = ChatMessage(content: "消息 \(i)", isFromUser: true, messageType: .text)
            historyManager.saveMessage(message)
        }
        
        let stats = await historyManager.getSessionStats()
        
        XCTAssertEqual(stats.messageCount, 3)
        XCTAssertNotNil(stats.lastMessageTime)
    }
    
    // MARK: - 数据模型测试
    
    func testChatHistoryModel() throws {
        let history = ChatHistory(
            content: "测试内容",
            isFromUser: true,
            messageType: "text",
            imageDescription: nil,
            sessionId: "test"
        )
        
        XCTAssertEqual(history.content, "测试内容")
        XCTAssertTrue(history.isFromUser)
        XCTAssertEqual(history.messageType, "text")
        XCTAssertEqual(history.sessionId, "test")
        XCTAssertNotNil(history.id)
        XCTAssertNotNil(history.timestamp)
    }
    
    func testChatHistoryToMessage() throws {
        let history = ChatHistory(
            content: "测试内容",
            isFromUser: false,
            messageType: "text"
        )
        
        let message = history.toChatMessage()
        
        XCTAssertEqual(message.content, "测试内容")
        XCTAssertFalse(message.isFromUser)
        XCTAssertEqual(message.messageType, .text)
    }
    
    func testChatHistoryRAGText() throws {
        let userHistory = ChatHistory(
            content: "你好",
            isFromUser: true,
            messageType: "text"
        )
        
        let aiHistory = ChatHistory(
            content: "嗨！",
            isFromUser: false,
            messageType: "text"
        )
        
        XCTAssertEqual(userHistory.ragText, "用户: 你好")
        XCTAssertEqual(aiHistory.ragText, "AI: 嗨！")
    }
    
    func testImageHistoryRAGText() throws {
        let imageHistory = ChatHistory(
            content: "",
            isFromUser: true,
            messageType: "image",
            imageDescription: "[图像]:一张海边照片"
        )
        
        XCTAssertEqual(imageHistory.ragText, "用户: [图像]:一张海边照片")
    }
}

// MARK: - 辅助扩展
extension ChatHistoryManager {
    func setValue(_ value: Any?, forKey key: String) {
        // 仅用于测试的辅助方法
        // 在实际应用中不应该使用这种方式
    }
}
