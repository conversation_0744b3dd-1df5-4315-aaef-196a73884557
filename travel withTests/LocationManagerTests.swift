//
//  LocationManagerTests.swift
//  travel withTests
//
//  Created by AI Assistant on 2025/7/30.
//

import XCTest
import CoreLocation
@testable import travel_with

class LocationManagerTests: XCTestCase {
    var locationManager: LocationManager!
    
    override func setUp() {
        super.setUp()
        locationManager = LocationManager()
    }
    
    override func tearDown() {
        locationManager = nil
        super.tearDown()
    }
    
    // MARK: - 测试航向功能
    
    func testHeadingUpdatesAvailability() {
        // 测试设备是否支持航向监听
        let isHeadingAvailable = CLLocationManager.headingAvailable()
        print("🧭 设备支持航向监听: \(isHeadingAvailable)")
        
        // 在模拟器中可能不支持，但在真机上应该支持
        if isHeadingAvailable {
            XCTAssertTrue(isHeadingAvailable, "设备应该支持航向监听")
        } else {
            print("⚠️ 当前设备不支持航向监听（可能是模拟器）")
        }
    }
    
    func testLocationManagerInitialization() {
        // 测试LocationManager初始化
        XCTAssertNotNil(locationManager, "LocationManager应该成功初始化")
        XCTAssertEqual(locationManager.deviceHeading, 0, "初始设备朝向应该为0")
        XCTAssertEqual(locationManager.userCourse, 0, "初始移动方向应该为0")
    }
    
    func testHeadingUpdateMethods() {
        // 测试航向更新方法存在
        XCTAssertNoThrow(locationManager.startHeadingUpdates(), "startHeadingUpdates方法应该存在")
        XCTAssertNoThrow(locationManager.stopHeadingUpdates(), "stopHeadingUpdates方法应该存在")
    }
    
    // MARK: - 模拟航向更新测试
    
    func testHeadingUpdateSimulation() {
        // 模拟航向更新
        let expectation = XCTestExpectation(description: "航向更新")
        
        // 创建模拟的航向数据
        let mockHeading = MockCLHeading(magneticHeading: 45.0, headingAccuracy: 5.0)
        
        // 模拟航向更新
        locationManager.locationManager(CLLocationManager(), didUpdateHeading: mockHeading)
        
        // 验证航向是否正确更新
        XCTAssertEqual(locationManager.deviceHeading, 45.0, "设备朝向应该更新为45度")
        
        expectation.fulfill()
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testLocationCourseUpdate() {
        // 测试移动方向更新
        let expectation = XCTestExpectation(description: "移动方向更新")
        
        // 创建模拟的位置数据（包含course信息）
        let mockLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            altitude: 0,
            horizontalAccuracy: 5,
            verticalAccuracy: 5,
            course: 90.0, // 向东
            speed: 5.0,
            timestamp: Date()
        )
        
        // 模拟位置更新
        locationManager.locationManager(CLLocationManager(), didUpdateLocations: [mockLocation])
        
        // 验证移动方向是否正确更新
        XCTAssertEqual(locationManager.userCourse, 90.0, "移动方向应该更新为90度（向东）")
        
        expectation.fulfill()
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testArrowRotationLogic() {
        // 测试箭头旋转逻辑
        
        // 创建一个模拟的MapNavigationView来测试getArrowRotation逻辑
        // 由于getArrowRotation是private方法，我们通过模拟不同的状态来测试
        
        // 1. 测试静止状态（应该使用设备朝向）
        let staticLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            altitude: 0,
            horizontalAccuracy: 5,
            verticalAccuracy: 5,
            course: -1, // 无效的course
            speed: 0.0, // 静止
            timestamp: Date()
        )
        
        locationManager.locationManager(CLLocationManager(), didUpdateLocations: [staticLocation])
        
        let mockHeading = MockCLHeading(magneticHeading: 120.0, headingAccuracy: 5.0)
        locationManager.locationManager(CLLocationManager(), didUpdateHeading: mockHeading)
        
        XCTAssertEqual(locationManager.deviceHeading, 120.0, "设备朝向应该更新")
        
        // 2. 测试移动状态（应该使用移动方向）
        let movingLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
            altitude: 0,
            horizontalAccuracy: 5,
            verticalAccuracy: 5,
            course: 180.0, // 向南
            speed: 2.0, // 移动中
            timestamp: Date()
        )
        
        locationManager.locationManager(CLLocationManager(), didUpdateLocations: [movingLocation])
        
        XCTAssertEqual(locationManager.userCourse, 180.0, "移动方向应该更新为180度（向南）")
    }
}

// MARK: - 模拟类

class MockCLHeading: CLHeading {
    private let _magneticHeading: CLLocationDirection
    private let _headingAccuracy: CLLocationAccuracy
    
    init(magneticHeading: CLLocationDirection, headingAccuracy: CLLocationAccuracy) {
        self._magneticHeading = magneticHeading
        self._headingAccuracy = headingAccuracy
        super.init()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override var magneticHeading: CLLocationDirection {
        return _magneticHeading
    }
    
    override var headingAccuracy: CLLocationAccuracy {
        return _headingAccuracy
    }
}
