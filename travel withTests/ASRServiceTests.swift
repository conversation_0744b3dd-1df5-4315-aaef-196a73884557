//
//  ASRServiceTests.swift
//  travel withTests
//
//  Created by AI Assistant on 2025/7/31.
//

import XCTest
import AVFoundation
import Speech
@testable import travel_with

@MainActor
final class ASRServiceTests: XCTestCase {
    var asrService: ASRService!
    
    override func setUpWithError() throws {
        super.setUp()
        asrService = ASRService()
    }
    
    override func tearDownWithError() throws {
        asrService.stopRecording()
        asrService = nil
        super.tearDown()
    }
    
    // MARK: - 初始化测试
    func testASRServiceInitialization() throws {
        XCTAssertNotNil(asrService, "ASRService应该成功初始化")
        XCTAssertFalse(asrService.isRecording, "初始状态应该不在录音")
        XCTAssertFalse(asrService.isConnected, "初始状态应该未连接")
        XCTAssertTrue(asrService.recognizedText.isEmpty, "初始识别文本应该为空")
        XCTAssertTrue(asrService.finalText.isEmpty, "初始最终文本应该为空")
    }
    
    // MARK: - 权限测试
    func testMicrophonePermission() async throws {
        // 测试麦克风权限申请
        let permission = await AVAudioSession.sharedInstance().requestRecordPermission()
        
        // 在测试环境中，权限状态可能不确定，所以我们只测试方法调用不崩溃
        XCTAssertNoThrow(permission, "麦克风权限申请不应该崩溃")
    }
    
    func testSpeechRecognitionPermission() throws {
        // 测试语音识别权限状态
        let authStatus = SFSpeechRecognizer.authorizationStatus()
        
        // 验证权限状态是有效的枚举值
        let validStatuses: [SFSpeechRecognizerAuthorizationStatus] = [
            .notDetermined, .denied, .restricted, .authorized
        ]
        XCTAssertTrue(validStatuses.contains(authStatus), "语音识别权限状态应该是有效值")
    }
    
    // MARK: - 配置测试
    func testVolcanoEngineConfiguration() throws {
        // 测试火山引擎配置是否正确设置
        XCTAssertNotNil(asrService, "ASRService应该包含火山引擎配置")
        
        // 测试默认使用原生语音识别
        XCTAssertTrue(asrService.useNativeSpeech, "默认应该使用iOS原生语音识别")
    }
    
    // MARK: - 回调设置测试
    func testCallbackSetup() throws {
        var finalResultCalled = false
        var partialResultCalled = false
        var errorCalled = false
        
        // 设置回调
        asrService.onFinalResult = { text in
            finalResultCalled = true
            XCTAssertFalse(text.isEmpty, "最终结果不应该为空")
        }
        
        asrService.onPartialResult = { text in
            partialResultCalled = true
        }
        
        asrService.onError = { error in
            errorCalled = true
            XCTAssertFalse(error.isEmpty, "错误信息不应该为空")
        }
        
        // 模拟回调调用
        asrService.onFinalResult?("测试文本")
        asrService.onPartialResult?("部分文本")
        asrService.onError?("测试错误")
        
        XCTAssertTrue(finalResultCalled, "最终结果回调应该被调用")
        XCTAssertTrue(partialResultCalled, "部分结果回调应该被调用")
        XCTAssertTrue(errorCalled, "错误回调应该被调用")
    }
    
    // MARK: - 状态管理测试
    func testRecordingStateManagement() throws {
        // 测试录音状态初始值
        XCTAssertFalse(asrService.isRecording, "初始应该不在录音状态")
        
        // 测试状态变更（不实际启动录音，只测试状态）
        // 注意：实际的录音测试需要在真机上进行，这里只测试状态管理逻辑
    }
    
    // MARK: - 错误处理测试
    func testErrorHandling() throws {
        var errorReceived: String?
        
        asrService.onError = { error in
            errorReceived = error
        }
        
        // 模拟错误情况
        asrService.onError?("测试错误消息")
        
        XCTAssertEqual(errorReceived, "测试错误消息", "错误消息应该正确传递")
    }
    
    // MARK: - 文本处理测试
    func testTextProcessing() throws {
        // 测试识别文本的设置和获取
        asrService.recognizedText = "测试识别文本"
        XCTAssertEqual(asrService.recognizedText, "测试识别文本", "识别文本应该正确设置")
        
        asrService.finalText = "最终测试文本"
        XCTAssertEqual(asrService.finalText, "最终测试文本", "最终文本应该正确设置")
    }
    
    // MARK: - 语音识别器可用性测试
    func testSpeechRecognizerAvailability() throws {
        let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))
        
        // 测试中文语音识别器是否可用
        if let recognizer = speechRecognizer {
            // 在某些测试环境中，语音识别器可能不可用，这是正常的
            print("语音识别器状态: \(recognizer.isAvailable ? "可用" : "不可用")")
        } else {
            print("无法创建中文语音识别器")
        }
        
        // 这个测试主要确保代码不会崩溃
        XCTAssertNoThrow(speechRecognizer, "创建语音识别器不应该崩溃")
    }
    
    // MARK: - 音频会话测试
    func testAudioSessionSetup() throws {
        let audioSession = AVAudioSession.sharedInstance()
        
        // 测试音频会话配置
        XCTAssertNoThrow(try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth]), "音频会话配置不应该失败")
    }
    
    // MARK: - 清理测试
    func testCleanup() throws {
        // 测试停止录音时的清理工作
        asrService.recognizedText = "测试文本"
        asrService.finalText = "最终文本"
        asrService.errorMessage = "错误信息"
        
        // 停止录音应该保持文本内容（用于显示结果）
        asrService.stopRecording()
        
        // 验证状态正确更新
        XCTAssertFalse(asrService.isRecording, "停止录音后应该不在录音状态")
    }
    
    // MARK: - 性能测试
    func testPerformance() throws {
        // 测试ASR服务初始化性能
        measure {
            let testService = ASRService()
            _ = testService.isRecording
        }
    }
}

// MARK: - 模拟测试扩展
extension ASRServiceTests {
    
    // 模拟语音识别结果的测试
    func testMockSpeechRecognitionResult() throws {
        let expectation = XCTestExpectation(description: "语音识别结果回调")
        
        asrService.onFinalResult = { text in
            XCTAssertEqual(text, "你好世界", "应该收到正确的识别结果")
            expectation.fulfill()
        }
        
        // 模拟识别结果
        asrService.onFinalResult?("你好世界")
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // 模拟部分识别结果的测试
    func testMockPartialRecognitionResult() throws {
        let expectation = XCTestExpectation(description: "部分识别结果回调")
        
        asrService.onPartialResult = { text in
            XCTAssertFalse(text.isEmpty, "部分识别结果不应该为空")
            expectation.fulfill()
        }
        
        // 模拟部分识别结果
        asrService.onPartialResult?("你好")
        
        wait(for: [expectation], timeout: 1.0)
    }
}
