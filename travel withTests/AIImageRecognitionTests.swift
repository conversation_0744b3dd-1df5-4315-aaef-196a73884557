//
//  AIImageRecognitionTests.swift
//  travel withTests
//
//  Created by AI Assistant on 2025/7/30.
//

import XCTest
import UIKit
@testable import travel_with

@MainActor
final class AIImageRecognitionTests: XCTestCase {
    
    var aiService: AIService!
    
    override func setUpWithError() throws {
        aiService = AIService()
    }
    
    override func tearDownWithError() throws {
        aiService = nil
    }
    
    // MARK: - 图像识别功能测试
    
    func testImageRecognitionModelConfiguration() throws {
        // 测试专用图像识别模型配置是否正确
        let mirror = Mirror(reflecting: aiService)
        let imageRecognitionModel = mirror.children.first { $0.label == "imageRecognitionModel" }?.value as? String
        
        XCTAssertEqual(imageRecognitionModel, "ep-20250730213933-4s55h", "专用图像识别模型配置应该正确")
    }
    
    func testSendImageMessageWithValidImage() async throws {
        // 创建测试图像
        let testImage = createTestImage()
        
        // 记录初始消息数量
        let initialMessageCount = aiService.messages.count
        
        // 发送图像消息
        await aiService.sendImageMessage(testImage, withText: "这是什么？")
        
        // 验证消息数量增加
        XCTAssertGreaterThan(aiService.messages.count, initialMessageCount, "发送图像后应该增加消息")
        
        // 验证用户消息类型
        let userMessage = aiService.messages.last(where: { $0.isFromUser })
        XCTAssertEqual(userMessage?.messageType, .image, "用户消息应该是图像类型")
        XCTAssertNotNil(userMessage?.imageData, "用户消息应该包含图像数据")
    }
    
    func testImageCompressionQuality() throws {
        // 创建测试图像
        let testImage = createTestImage()
        
        // 测试图像压缩
        let compressedData = testImage.jpegData(compressionQuality: 0.7)
        XCTAssertNotNil(compressedData, "图像应该能够成功压缩")
        
        // 验证压缩后的图像可以重新创建
        let recreatedImage = UIImage(data: compressedData!)
        XCTAssertNotNil(recreatedImage, "压缩后的数据应该能够重新创建图像")
    }
    
    func testBase64ImageConversion() throws {
        // 创建测试图像
        let testImage = createTestImage()
        
        // 获取图像数据
        guard let imageData = testImage.jpegData(compressionQuality: 0.7) else {
            XCTFail("无法获取图像数据")
            return
        }
        
        // 转换为base64
        let base64String = imageData.base64EncodedString()
        let imageURL = "data:image/jpeg;base64,\(base64String)"
        
        // 验证base64格式
        XCTAssertTrue(imageURL.hasPrefix("data:image/jpeg;base64,"), "应该生成正确的base64图像URL")
        XCTAssertFalse(base64String.isEmpty, "base64字符串不应该为空")
    }
    
    func testARSnapshotAnalysis() async throws {
        // 创建测试AR截图
        let testImage = createTestImage()
        
        // 测试AR截图分析
        let analysisResult = await aiService.analyzeARSnapshot(testImage)
        
        // 验证分析结果
        XCTAssertFalse(analysisResult.isEmpty, "AR截图分析应该返回非空结果")
    }
    
    func testImageMessageWithEmptyText() async throws {
        // 创建测试图像
        let testImage = createTestImage()
        
        // 记录初始消息数量
        let initialMessageCount = aiService.messages.count
        
        // 发送没有文本的图像消息
        await aiService.sendImageMessage(testImage, withText: "")
        
        // 验证消息数量增加
        XCTAssertGreaterThan(aiService.messages.count, initialMessageCount, "发送图像后应该增加消息")
        
        // 验证用户消息
        let userMessage = aiService.messages.last(where: { $0.isFromUser })
        XCTAssertEqual(userMessage?.messageType, .image, "用户消息应该是图像类型")
        XCTAssertEqual(userMessage?.content, "", "没有文本时内容应该为空")
    }
    
    func testLoadingStateManagement() async throws {
        // 创建测试图像
        let testImage = createTestImage()
        
        // 验证初始状态
        XCTAssertFalse(aiService.isLoading, "初始状态应该不是加载中")
        
        // 开始发送图像消息（不等待完成）
        let sendTask = Task {
            await aiService.sendImageMessage(testImage, withText: "测试加载状态")
        }
        
        // 短暂延迟后检查加载状态
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        
        // 等待任务完成
        await sendTask.value
        
        // 验证最终状态
        XCTAssertFalse(aiService.isLoading, "完成后应该不是加载中状态")
    }
    
    // MARK: - 辅助方法
    
    private func createTestImage() -> UIImage {
        // 创建一个简单的测试图像
        let size = CGSize(width: 100, height: 100)
        UIGraphicsBeginImageContext(size)
        
        // 绘制一个简单的矩形
        let context = UIGraphicsGetCurrentContext()
        context?.setFillColor(UIColor.blue.cgColor)
        context?.fill(CGRect(origin: .zero, size: size))
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return image ?? UIImage()
    }
}

// MARK: - 性能测试

extension AIImageRecognitionTests {
    
    func testImageCompressionPerformance() throws {
        // 创建较大的测试图像
        let largeImage = createLargeTestImage()
        
        measure {
            _ = largeImage.jpegData(compressionQuality: 0.7)
        }
    }
    
    func testBase64ConversionPerformance() throws {
        // 创建测试图像数据
        let testImage = createTestImage()
        guard let imageData = testImage.jpegData(compressionQuality: 0.7) else {
            XCTFail("无法获取图像数据")
            return
        }
        
        measure {
            _ = imageData.base64EncodedString()
        }
    }
    
    private func createLargeTestImage() -> UIImage {
        // 创建一个较大的测试图像用于性能测试
        let size = CGSize(width: 1000, height: 1000)
        UIGraphicsBeginImageContext(size)
        
        let context = UIGraphicsGetCurrentContext()
        context?.setFillColor(UIColor.red.cgColor)
        context?.fill(CGRect(origin: .zero, size: size))
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return image ?? UIImage()
    }
}
