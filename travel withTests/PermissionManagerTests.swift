//
//  PermissionManagerTests.swift
//  travel withTests
//
//  Created by AI Assistant on 2025/7/31.
//

import XCTest
import AVFoundation
import Speech
@testable import travel_with

@MainActor
final class PermissionManagerTests: XCTestCase {
    var permissionManager: PermissionManager!
    
    override func setUpWithError() throws {
        super.setUp()
        permissionManager = PermissionManager()
    }
    
    override func tearDownWithError() throws {
        permissionManager = nil
        super.tearDown()
    }
    
    // MARK: - 初始化测试
    func testPermissionManagerInitialization() throws {
        XCTAssertNotNil(permissionManager, "PermissionManager应该成功初始化")
        XCTAssertFalse(permissionManager.showingPermissionAlert, "初始状态不应该显示权限提示")
        XCTAssertTrue(permissionManager.permissionAlertMessage.isEmpty, "初始权限提示消息应该为空")
    }
    
    // MARK: - 权限状态检查测试
    func testCheckCurrentPermissions() throws {
        // 测试权限状态检查不会崩溃
        XCTAssertNoThrow(permissionManager.checkCurrentPermissions(), "检查权限状态不应该崩溃")
        
        // 验证权限状态是有效的枚举值
        let validStatuses: [PermissionStatus] = [.notDetermined, .granted, .denied, .restricted]
        XCTAssertTrue(validStatuses.contains(permissionManager.microphoneStatus), "麦克风权限状态应该是有效值")
        XCTAssertTrue(validStatuses.contains(permissionManager.speechRecognitionStatus), "语音识别权限状态应该是有效值")
    }
    
    // MARK: - 权限状态转换测试
    func testPermissionStatusConversion() throws {
        // 测试权限状态描述
        XCTAssertEqual(permissionManager.getPermissionStatusText(.notDetermined), "未确定")
        XCTAssertEqual(permissionManager.getPermissionStatusText(.granted), "已授权")
        XCTAssertEqual(permissionManager.getPermissionStatusText(.denied), "已拒绝")
        XCTAssertEqual(permissionManager.getPermissionStatusText(.restricted), "受限制")
        
        // 测试权限状态颜色
        XCTAssertEqual(permissionManager.getPermissionStatusColor(.granted), .green)
        XCTAssertEqual(permissionManager.getPermissionStatusColor(.denied), .red)
        XCTAssertEqual(permissionManager.getPermissionStatusColor(.restricted), .red)
        XCTAssertEqual(permissionManager.getPermissionStatusColor(.notDetermined), .orange)
    }
    
    // MARK: - 权限申请测试
    func testRequestAllPermissions() async throws {
        // 测试权限申请方法不会崩溃
        await XCTAssertNoThrowAsync(
            await permissionManager.requestAllPermissions(),
            "申请权限不应该崩溃"
        )
    }
    
    // MARK: - 设置页面跳转测试
    func testOpenSettings() throws {
        // 测试打开设置方法不会崩溃
        XCTAssertNoThrow(permissionManager.openSettings(), "打开设置不应该崩溃")
    }
    
    // MARK: - 权限状态更新测试
    func testPermissionStatusUpdate() throws {
        // 模拟权限状态变化
        permissionManager.microphoneStatus = .granted
        permissionManager.speechRecognitionStatus = .granted
        
        // 手动触发状态更新（通过私有方法的反射调用或公共接口）
        permissionManager.checkCurrentPermissions()
        
        // 验证状态更新
        // 注意：在测试环境中，实际权限状态可能与设置的不同
        // 这里主要测试方法调用不会崩溃
    }
    
    // MARK: - 回调测试
    func testPermissionCompletionCallback() throws {
        var callbackCalled = false
        
        permissionManager.onPermissionsCompleted = {
            callbackCalled = true
        }
        
        // 模拟权限申请完成
        permissionManager.onPermissionsCompleted?()
        
        XCTAssertTrue(callbackCalled, "权限完成回调应该被调用")
    }
    
    // MARK: - 权限提示测试
    func testPermissionAlert() throws {
        // 模拟权限被拒绝的情况
        permissionManager.microphoneStatus = .denied
        permissionManager.speechRecognitionStatus = .denied
        
        // 这里我们无法直接测试私有方法，但可以测试相关的公共状态
        XCTAssertEqual(permissionManager.microphoneStatus, .denied)
        XCTAssertEqual(permissionManager.speechRecognitionStatus, .denied)
    }
    
    // MARK: - 性能测试
    func testPermissionManagerPerformance() throws {
        measure {
            let manager = PermissionManager()
            manager.checkCurrentPermissions()
        }
    }
}

// MARK: - 异步测试扩展
extension XCTestCase {
    func XCTAssertNoThrowAsync<T>(
        _ expression: @autoclosure () async throws -> T,
        _ message: @autoclosure () -> String = "",
        file: StaticString = #filePath,
        line: UInt = #line
    ) async {
        do {
            _ = try await expression()
        } catch {
            XCTFail("Async expression threw error: \(error). \(message())", file: file, line: line)
        }
    }
}

// MARK: - 权限请求视图测试
final class PermissionRequestViewTests: XCTestCase {
    
    func testPermissionRequestViewCreation() throws {
        // 测试权限请求视图可以正常创建
        let view = PermissionRequestView {
            print("测试完成回调")
        }
        
        XCTAssertNotNil(view, "PermissionRequestView应该能够正常创建")
    }
    
    func testPermissionRowViewCreation() throws {
        // 测试权限行视图可以正常创建
        let view = PermissionRowView(
            icon: "mic.fill",
            title: "麦克风",
            description: "用于录制语音",
            status: .notDetermined
        )
        
        XCTAssertNotNil(view, "PermissionRowView应该能够正常创建")
    }
}

// MARK: - 启动画面测试
final class LaunchScreenViewTests: XCTestCase {
    
    func testLaunchScreenViewCreation() throws {
        // 测试启动画面可以正常创建
        let view = LaunchScreenView()
        
        XCTAssertNotNil(view, "LaunchScreenView应该能够正常创建")
    }
}
